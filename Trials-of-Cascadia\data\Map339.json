{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "RNR2_WorldStories_FULL_Loop", "pan": 0, "pitch": 100, "volume": 30}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 10, "note": "<Zoom: 150%>\n<No Weather>\n<No Dust Cloud>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 11, "data": [5984, 6180, 6204, 6204, 6204, 6204, 6204, 6204, 6204, 6184, 6176, 6180, 6214, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6216, 6184, 6200, 6278, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6275, 6192, 6200, 6284, 3283, 3281, 3281, 3281, 3281, 3281, 3285, 6281, 6192, 6200, 3268, 3274, 3714, 3700, 3700, 3700, 3716, 3265, 3268, 6192, 6200, 3248, 3272, 3696, 3680, 3680, 3680, 3704, 3264, 3248, 6192, 6200, 3248, 3272, 3720, 3708, 3708, 3708, 3718, 3264, 3248, 6192, 6178, 6212, 3250, 3269, 3281, 3270, 3268, 3271, 3249, 6210, 6177, 6176, 6178, 6196, 6196, 6196, 6196, 6212, 3292, 6210, 6177, 0, 6176, 6176, 6176, 6176, 6176, 6176, 6178, 6196, 6177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 0, 33, 0, 33, 0, 0, 0, 0, 0, 390, 391, 0, 40, 0, 390, 391, 0, 0, 0, 0, 398, 399, 0, 0, 0, 398, 399, 0, 0, 0, 288, 0, 0, 0, 0, 0, 0, 0, 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 4, "y": 3}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 2, "y": 4}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 1, "y": 5}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 126, "indent": 0, "parameters": [53, 0, 0, 10]}, {"code": 357, "indent": 0, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 0, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 0, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 4, "y": 5}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 126, "indent": 0, "parameters": [53, 0, 0, 10]}, {"code": 357, "indent": 0, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 0, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 0, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 5, "y": 5}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 5, "y": 3}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 6, "y": 3}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 3, "y": 6}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 8, "y": 4}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 288, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 296, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 9, "y": 5}, null, null, null, null, null, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 100, 19, 6, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 8}]}
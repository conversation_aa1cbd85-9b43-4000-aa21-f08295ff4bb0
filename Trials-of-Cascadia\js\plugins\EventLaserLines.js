//=============================================================================
// EventLaserLines.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc Creates animated laser beams that emit from single events
 * <AUTHOR> Name
 * @version 1.0.0
 * 
 * @param laserColor
 * @text Laser Color
 * @desc The color of the laser lines (hex format).
 * @default #FF0066
 * @type string
 * 
 * @param laserWidth
 * @text Laser Width
 * @desc The width of the laser lines in pixels.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 * 
 * @param laserOpacity
 * @text Laser Opacity
 * @desc The opacity of the laser lines (0-255).
 * @default 180
 * @type number
 * @min 50
 * @max 255
 * 
 * @param laserPulse
 * @text Laser Pulse
 * @desc Whether the laser should pulse/flicker.
 * @default true
 * @type boolean
 * 
 * @param laserPulseSpeed
 * @text Pulse Speed
 * @desc How fast the laser pulses (higher = faster).
 * @default 0.1
 * @type number
 * @min 0.01
 * @max 0.5
 * @decimals 2
 * 
 * @param laserGlow
 * @text Laser Glow
 * @desc Whether to add a glow effect around the laser.
 * @default true
 * @type boolean
 * 
 * @param laserGlowSize
 * @text Glow Size
 * @desc The size of the glow effect around the laser.
 * @default 8
 * @type number
 * @min 0
 * @max 20
 * 
 * @param laserFloat
 * @text Laser Float
 * @desc Whether the laser should have a floating animation.
 * @default true
 * @type boolean
 * 
 * @param laserParticles
 * @text Laser Particles
 * @desc Whether to add animated particles along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserFlicker
 * @text Laser Flicker
 * @desc Whether the laser should have a dangerous flicker effect.
 * @default true
 * @type boolean
 * 
 * @param laserFlickerIntensity
 * @text Flicker Intensity
 * @desc How intense the flicker effect is (0.1-0.8).
 * @default 0.3
 * @type number
 * @min 0.1
 * @max 0.8
 * @decimals 1
 * 
 * @param laserHeatDistortion
 * @text Heat Distortion
 * @desc Whether to add heat distortion effects to the laser.
 * @default true
 * @type boolean
 * 
 * @param laserElectricArcs
 * @text Electric Arcs
 * @desc Whether to add dangerous electric arc effects along the laser.
 * @default true
 * @type boolean
 * 
 * @param laserWarningPulse
 * @text Warning Pulse
 * @desc Whether the laser should flash warning red colors.
 * @default true
 * @type boolean
 * 
 * @help
 * ============================================================================
 * Event Laser Lines Plugin
 * ============================================================================
 * 
 * This plugin creates animated laser beams that emit from single events
 * 
 * ============================================================================
 * Setup Instructions
 * ============================================================================
 * 
 * 1. Place an event where you want the laser to emit from
 * 2. Add a notetag to the event:
 * 
 * Basic laser:
 * <laser>
 * 
 * Custom laser:
 * <laser:direction=right,length=100,color=#00FFFF>
 * 
 * Dangerous laser with all effects:
 * <laser:direction=up,length=120,color=#FF0000,flicker=true,electricArcs=true,warningPulse=true>
 * 
 * Multiple lasers on same event:
 * <laser 1>
 * <laser 2:direction=up,length=80>
 * 
 * ============================================================================
 * Notetag Examples
 * ============================================================================
 * 
 * Basic laser:
 * <laser>
 * 
 * Directional laser:
 * <laser:direction=right,length=120>
 * <laser:direction=up,length=80>
 * <laser:direction=left,length=100>
 * <laser:direction=down,length=60>
 * 
 * Custom laser properties:
 * <laser:direction=right,length=100,color=#00FFFF,width=5,pulse=false>
 * 
 * Multiple lasers:
 * <laser 1:direction=right,length=100>
 * <laser 2:direction=up,length=80>
 * <laser 3:direction=left,length=120>
 * 
 * ============================================================================
 * Plugin Parameters
 * ============================================================================
 * 
 * - laserColor: Base color of all lasers (can be overridden per laser)
 * - laserWidth: Default width of laser lines
 * - laserOpacity: Default opacity of laser lines
 * - laserPulse: Whether lasers pulse by default
 * - laserPulseSpeed: Speed of the pulse effect
 * - laserGlow: Whether to add glow effects
 * - laserGlowSize: Size of glow effects
 * 
 * ============================================================================
 * Version History
 * ============================================================================
 * 
 * v1.0.0 - Initial release
 * 
 * ============================================================================
 */

(() => {
    'use strict';

    //=============================================================================
    // Plugin Parameters
    //=============================================================================
    const parameters = PluginManager.parameters('EventLaserLines');
    const LASER_COLOR = parameters['laserColor'] || '#FF0066';
    const LASER_WIDTH = Number(parameters['laserWidth']) || 3;
    const LASER_OPACITY = Number(parameters['laserOpacity']) || 180;
    const LASER_PULSE = parameters['laserPulse'] === 'true';
    const LASER_PULSE_SPEED = Number(parameters['laserPulseSpeed']) || 0.1;
    const LASER_GLOW = parameters['laserGlow'] === 'true';
    const LASER_GLOW_SIZE = Number(parameters['laserGlowSize']) || 8;
    const LASER_FLOAT = parameters['laserFloat'] === 'true';
    const LASER_PARTICLES = parameters['laserParticles'] === 'true';
    const LASER_FLICKER = parameters['laserFlicker'] === 'true';
    const LASER_FLICKER_INTENSITY = Number(parameters['laserFlickerIntensity']) || 0.3;
    const LASER_HEAT_DISTORTION = parameters['laserHeatDistortion'] === 'true';
    const LASER_ELECTRIC_ARCS = parameters['laserElectricArcs'] === 'true';
    const LASER_WARNING_PULSE = parameters['laserWarningPulse'] === 'true';

    //=============================================================================
    // Laser Line Sprite
    //=============================================================================
    class LaserLine extends Sprite {
        constructor(x, y, direction, length, settings) {
            super();
            console.log(`EventLaserLines: Creating LaserLine at (${x}, ${y}) direction=${direction} length=${length}`);
            console.log('EventLaserLines: LaserLine settings:', settings);

            this.x = x;
            this.y = y;
            this.originalY = y; // Store original Y for floating animation
            this.direction = direction;
            this.length = length;
            this.settings = settings;
            this.pulseTime = 0;
            this.glowTime = 0;
            this.floatTime = 0;
            this.heatTime = 0;
            this.flickerTime = 0;
            this.arcTime = 0;
            this.warningTime = 0;

            this.createLaserBitmap();

            console.log('EventLaserLines: LaserLine created with bitmap:', this.bitmap);
            console.log('EventLaserLines: LaserLine final properties:', {
                x: this.x,
                y: this.y,
                width: this.width,
                height: this.height,
                visible: this.visible,
                opacity: this.opacity
            });
        }

        createLaserBitmap() {
            // Get direction angle
            const directionAngles = {
                'right': 0,
                'up': -Math.PI / 2,
                'left': Math.PI,
                'down': Math.PI / 2
            };
            
            const angle = directionAngles[this.direction] || 0;
            const length = Math.max(this.length, 10);
            const height = Math.max(this.settings.width || LASER_WIDTH, 3);
            
            // Create bitmap with extra space for glow
            const extraSpace = this.settings.glow ? this.settings.glowSize * 2 : 0;
            const width = length + extraSpace;
            const bitmapHeight = height + extraSpace;
            
            const bitmap = new Bitmap(width, bitmapHeight);
            const ctx = bitmap.context;
            
            // Center the drawing context
            ctx.translate(extraSpace / 2, bitmapHeight / 2);
            
            // Apply rotation
            ctx.rotate(angle);
            
            // Create laser line
            this.drawLaserLine(ctx, length);
            
            // Add glow effect if enabled
            if (this.settings.glow) {
                this.drawLaserGlow(ctx, length);
            }
            
            this.bitmap = bitmap;
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            
            // Ensure sprite has proper dimensions
            this.width = width;
            this.height = bitmapHeight;
        }

        drawLaserLine(ctx, length) {
            const width = this.settings.width || LASER_WIDTH;
            const color = this.settings.color || LASER_COLOR;
            const opacity = this.settings.opacity || LASER_OPACITY;
            
            // Convert hex to RGB
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);
            
            // Create realistic laser beam with multiple layers
            
            // 1. Outer atmospheric glow (very faint, wide)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.05})`;
            ctx.lineWidth = width * 4;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.15})`;
            ctx.lineWidth = width * 2.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 3. Inner beam glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.4})`;
            ctx.lineWidth = width * 1.5;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 4. Core laser beam (brightest, sharpest)
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity / 255 * 0.9})`;
            ctx.lineWidth = width;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 5. Intense white-hot core center
            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity / 255 * 0.6})`;
            ctx.lineWidth = Math.max(1, width * 0.4);
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 6. Add electric arc effects along the beam for danger
            if (this.settings.electricArcs !== false) {
                this.drawElectricArcs(ctx, length, r, g, b, opacity);
            }
        }

        drawLaserGlow(ctx, length) {
            const width = this.settings.width || LASER_WIDTH;
            const glowSize = this.settings.glowSize || LASER_GLOW_SIZE;
            const color = this.settings.color || LASER_COLOR;
            
            // Convert hex to RGB
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);
            
            // Create realistic atmospheric scattering effect
            // This simulates how laser light interacts with air particles
            
            // 1. Wide, very faint atmospheric glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.02)`;
            ctx.lineWidth = width + glowSize * 3;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 2. Medium atmospheric scattering
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.04)`;
            ctx.lineWidth = width + glowSize * 2;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // 3. Closer atmospheric effect
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.08)`;
            ctx.lineWidth = width + glowSize;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(length, 0);
            ctx.stroke();
            
            // Particle functionality removed - no more circles along the laser
        }
        

        
        drawElectricArcs(ctx, length, r, g, b, opacity) {
            // Optimized electric arcs - reduced complexity for maximum performance
            const arcCount = 2; // Reduced from 4 to 2 arcs for performance
            const arcIntensity = opacity / 255 * 0.9; // Brighter for visibility
            
            for (let i = 0; i < arcCount; i++) {
                const x = (i + 1) * (length / (arcCount + 1));
                const arcLength = 15; // Fixed size instead of random for performance
                const arcHeight = 12; // Fixed size instead of random for performance
                const segments = 3; // Fixed segments instead of random for performance
                
                // Draw jagged white core arc
                ctx.strokeStyle = `rgba(255, 255, 255, ${arcIntensity})`;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                // Create jagged path like the sparking plugin - centered on laser line
                const startX = x - arcLength / 2;
                const startY = 0; // Center on laser line (y=0)
                const endX = x + arcLength / 2;
                const endY = 0; // Center on laser line (y=0)
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                
                // Create jagged segments for realistic electric arc
                for (let j = 1; j < segments; j++) {
                    const t = j / segments;
                    const baseX = startX + (endX - startX) * t;
                    const baseY = startY + (endY - startY) * t;
                    
                    // Add random jaggedness perpendicular to the arc (like the sparking plugin)
                    const jag = (Math.random() - 0.5) * 8; // Random offset for jaggedness
                    const jagX = baseX;
                    const jagY = baseY + jag;
                    
                    ctx.lineTo(jagX, jagY);
                }
                
                ctx.lineTo(endX, endY);
                ctx.stroke();
                
                // Add colored glow around the white core (like the sparking plugin)
                ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${arcIntensity * 0.7})`;
                ctx.lineWidth = 4;
                ctx.stroke();
                
                // Secondary arcs removed for maximum performance optimization
            }
        }

        update() {
            super.update();
            
            // Update pulse effect
            if (this.settings.pulse !== false) {
                this.pulseTime += LASER_PULSE_SPEED;
                const pulseFactor = 0.7 + Math.sin(this.pulseTime) * 0.3;
                this.opacity = (this.settings.opacity || LASER_OPACITY) * pulseFactor;
            }
            
            // Update glow animation
            if (this.settings.glow && this.settings.particles) {
                this.glowTime = (this.glowTime || 0) + 0.05;
                const glowFactor = 0.8 + Math.sin(this.glowTime) * 0.2;
                this.scale.x = glowFactor;
                this.scale.y = glowFactor;
            }
            
            // Update floating animation
            if (this.settings.float) {
                this.floatTime = (this.floatTime || 0) + 0.03;
                const floatOffset = Math.sin(this.floatTime) * 2;
                this.y = this.originalY + floatOffset;
            }
            
            // Add subtle heat distortion effect
            this.heatTime = (this.heatTime || 0) + 0.08;
            const heatDistortion = Math.sin(this.heatTime) * 0.5;
            this.skew.x = heatDistortion * 0.01; // Very subtle skew effect
            
            // Add dangerous flicker effect
            if (this.settings.flicker !== false) {
                this.flickerTime = (this.flickerTime || 0) + 0.15;
                const flickerIntensity = LASER_FLICKER_INTENSITY;
                const flickerFactor = 1 + Math.sin(this.flickerTime * 3) * flickerIntensity;
                this.opacity = (this.settings.opacity || LASER_OPACITY) * flickerFactor;
            }
            
            // Electric arc animation - subtle effects without making laser invisible
            if (this.settings.electricArcs !== false) {
                this.arcTime = (this.arcTime || 0) + 0.02;
                // Minimal movement to reduce calculations
                this.rotation = Math.sin(this.arcTime) * 0.01;

                // Subtle intensity flickering instead of visibility flickering
                const arcFlicker = 0.9 + Math.sin(this.arcTime * 5) * 0.1;
                this.alpha = Math.min(1.0, this.alpha * arcFlicker);
            }
            
            // Add warning pulse effect (red flash)
            if (this.settings.warningPulse !== false) {
                this.warningTime = (this.warningTime || 0) + 0.08;
                // Flash between normal color and warning red
                if (Math.sin(this.warningTime) > 0) {
                    this.tint = 0xff0000; // Red warning tint
                } else {
                    this.tint = 0xffffff; // Normal color
                }
            }
        }
    }

    //=============================================================================
    // Laser Manager
    //=============================================================================
    class LaserManager {
        constructor(scene) {
            this.scene = scene;
            this.activeLasers = [];
            this.laserEvents = new Map();
            this.lastEventState = new Map(); // Track event states to detect changes
            this.needsUpdate = true; // Flag to control when to update

            // Create laser layer
            this.laserLayer = new Sprite();
            this.laserLayer.z = 2; // Between map tiles and characters

            // Try to add to the map layer specifically to get below characters
            if (scene._spriteset && scene._spriteset._tilemap) {
                // Add as child of the tilemap (map layer) to ensure it's below characters
                scene._spriteset._tilemap.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to tilemap (below characters)');
            } else if (scene._spriteset) {
                // Fallback to spriteset
                scene._spriteset.addChild(this.laserLayer);
                console.log('EventLaserLines: Laser layer added to spriteset (fallback)');
            }
        }

        initialize() {
            console.log('EventLaserLines: LaserManager initializing...');
            // Scan for laser events immediately
            this.scanEventsForLasers();
            this.updateLasers();
            console.log('EventLaserLines: LaserManager initialized with', this.activeLasers.length, 'active lasers');
        }

        // Check if events have changed and need laser updates
        hasEventsChanged() {
            // Check if we need to force an update
            if (this.needsUpdate) {
                return true;
            }

            // Check if any event positions or pages have changed
            for (const [eventId] of this.laserEvents) {
                const event = $gameMap.event(eventId);
                if (!event) continue;

                const currentState = {
                    x: event.x,
                    y: event.y,
                    pageIndex: this.getCurrentPageIndex(event),
                    direction: event.direction()
                };

                const lastState = this.lastEventState.get(eventId);
                if (!lastState ||
                    lastState.x !== currentState.x ||
                    lastState.y !== currentState.y ||
                    lastState.pageIndex !== currentState.pageIndex ||
                    lastState.direction !== currentState.direction) {

                    this.lastEventState.set(eventId, currentState);
                    return true;
                }
            }

            return false;
        }

        scanEventsForLasers() {
            console.log('EventLaserLines: Scanning events for lasers...');
            this.laserEvents.clear();

            // Scan all events for laser notetags
            const allEvents = $gameMap.events();
            console.log('EventLaserLines: Total events on map:', allEvents.length);

            allEvents.forEach((event, index) => {
                if (event && event.event()) {
                    const eventData = event.event();
                    const eventId = index + 1;
                    let allNotetags = [];

                    // Check event note field (always active regardless of page)
                    if (eventData.note) {
                        console.log(`EventLaserLines: Event ${eventId} has note:`, eventData.note);
                        const notetagsFromNote = this.parseLaserNotetags(eventData.note);
                        allNotetags = allNotetags.concat(notetagsFromNote);
                        if (notetagsFromNote.length > 0) {
                            console.log(`EventLaserLines: Found ${notetagsFromNote.length} laser notetags in note for event ${eventId}`);
                        }
                    }

                    // Check Comment commands ONLY in the currently active page
                    if (eventData.pages && eventData.pages.length > 0) {
                        // Get the current page index for this event
                        const currentPageIndex = this.getCurrentPageIndex(event);
                        console.log(`EventLaserLines: Event ${eventId} current page index:`, currentPageIndex);

                        if (currentPageIndex >= 0 && currentPageIndex < eventData.pages.length) {
                            const currentPage = eventData.pages[currentPageIndex];
                            const commentText = this.extractCommentText(currentPage.list);
                            if (commentText) {
                                console.log(`EventLaserLines: Event ${eventId} comment text:`, commentText);
                                const notetagsFromComments = this.parseLaserNotetags(commentText);
                                allNotetags = allNotetags.concat(notetagsFromComments);
                                if (notetagsFromComments.length > 0) {
                                    console.log(`EventLaserLines: Found ${notetagsFromComments.length} laser notetags in comments for event ${eventId}`);
                                }
                            }
                        }
                    }

                    if (allNotetags.length > 0) {
                        this.laserEvents.set(eventId, allNotetags);
                        console.log(`EventLaserLines: Event ${eventId} registered with ${allNotetags.length} laser notetags:`, allNotetags);
                    }
                }
            });

            console.log('EventLaserLines: Total laser events found:', this.laserEvents.size);
            console.log('EventLaserLines: Laser events map:', Array.from(this.laserEvents.entries()));
        }

        getCurrentPageIndex(event) {
            // Use RPG Maker's built-in method to get the current page index
            // This respects all the page conditions (switches, variables, etc.)
            if (event && event.findProperPageIndex) {
                return event.findProperPageIndex();
            }

            // Fallback: manually check page conditions
            const eventData = event.event();
            if (!eventData || !eventData.pages) return -1;

            for (let i = eventData.pages.length - 1; i >= 0; i--) {
                const page = eventData.pages[i];
                if (this.meetsConditions(page.conditions, event)) {
                    return i;
                }
            }

            return -1;
        }

        meetsConditions(conditions, event) {
            // Check if page conditions are met
            if (!conditions) return true;

            // Check switch conditions
            if (conditions.switch1Valid && !$gameSwitches.value(conditions.switch1Id)) {
                return false;
            }
            if (conditions.switch2Valid && !$gameSwitches.value(conditions.switch2Id)) {
                return false;
            }

            // Check variable conditions
            if (conditions.variableValid) {
                const variableValue = $gameVariables.value(conditions.variableId);
                if (variableValue < conditions.variableValue) {
                    return false;
                }
            }

            // Check self switch conditions
            if (conditions.selfSwitchValid && event) {
                const key = [$gameMap.mapId(), event._eventId, conditions.selfSwitchCh];
                if (!$gameSelfSwitches.value(key)) {
                    return false;
                }
            }

            return true;
        }

        extractCommentText(commandList) {
            let commentText = '';

            if (!commandList || !Array.isArray(commandList)) {
                return commentText;
            }

            for (let i = 0; i < commandList.length; i++) {
                const command = commandList[i];

                // Event command code 108 = Comment (first line)
                // Event command code 408 = Comment (continuation lines)
                if (command.code === 108 || command.code === 408) {
                    if (command.parameters && command.parameters[0]) {
                        commentText += command.parameters[0] + '\n';
                    }
                }
            }

            return commentText;
        }

        parseLaserNotetags(note) {
            console.log('EventLaserLines: Parsing notetags from:', note);
            const notetags = [];

            // Match both <laser> and <laser:...> formats
            const matches = note.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/g);
            console.log('EventLaserLines: Regex matches found:', matches);

            if (!matches) return notetags;

            matches.forEach((match) => {
                console.log('EventLaserLines: Processing match:', match);

                const fullMatch = match.match(/<laser(?:\s+(\d+))?(?::(.*?))?>/);
                if (!fullMatch) return;

                const laserId = fullMatch[1] ? Number(fullMatch[1]) : 1;
                const params = fullMatch[2] || '';

                console.log('EventLaserLines: Parsed laser ID:', laserId, 'params:', params);

                const settings = {
                    direction: 'right',
                    length: 100,
                    color: LASER_COLOR,
                    width: LASER_WIDTH,
                    opacity: LASER_OPACITY,
                    pulse: LASER_PULSE,
                    glow: LASER_GLOW,
                    glowSize: LASER_GLOW_SIZE,
                    float: LASER_FLOAT,
                    particles: LASER_PARTICLES
                };

                // Parse custom parameters
                if (params) {
                    params.split(',').forEach(param => {
                        const [key, value] = param.trim().split('=').map(p => p.trim());

                        if (key === 'direction') {
                            settings.direction = value;
                        } else if (key === 'length') {
                            settings.length = Number(value);
                        } else if (key === 'color') {
                            settings.color = value;
                        } else if (key === 'width') {
                            settings.width = Number(value);
                        } else if (key === 'opacity') {
                            settings.opacity = Number(value);
                        } else if (key === 'pulse') {
                            settings.pulse = value === 'true';
                        } else if (key === 'glow') {
                            settings.glow = value === 'true';
                        } else if (key === 'glowSize') {
                            settings.glowSize = Number(value);
                        } else if (key === 'float') {
                            settings.float = value === 'true';
                        } else if (key === 'particles') {
                            settings.particles = value === 'true';
                        }
                    });
                }

                const notetag = {
                    laserId: laserId,
                    settings: settings
                };

                console.log('EventLaserLines: Created notetag:', notetag);
                notetags.push(notetag);
            });

            console.log('EventLaserLines: Final notetags array:', notetags);
            return notetags;
        }

        updateLasers() {
            console.log('EventLaserLines: updateLasers() called');

            // Only update if events have changed
            if (!this.hasEventsChanged()) {
                console.log('EventLaserLines: No changes detected, skipping update');
                return;
            }

            console.log('EventLaserLines: Changes detected, updating lasers...');

            // Clear old lasers
            console.log('EventLaserLines: Clearing', this.activeLasers.length, 'old lasers');
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
            });
            this.activeLasers = [];

            // Re-scan events to get current page laser notetags
            this.scanEventsForLasers();

            console.log('EventLaserLines: Creating lasers for', this.laserEvents.size, 'events');

            // Create new lasers based on current laser events
            this.laserEvents.forEach((notetags, eventId) => {
                console.log(`EventLaserLines: Processing event ${eventId} with ${notetags.length} notetags`);

                const event = $gameMap.event(eventId);
                if (!event) {
                    console.warn(`EventLaserLines: Event ${eventId} not found in $gameMap.event()`);
                    return;
                }

                // Get event sprite position with error handling
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) {
                    console.warn(`EventLaserLines: Could not find sprite for event ${eventId}`);
                    console.log('EventLaserLines: Available spriteset:', this.scene._spriteset);
                    console.log('EventLaserLines: Event object:', event);
                    return;
                }

                const eventX = sprite.x;
                // Adjust Y to center of tile instead of bottom
                // RPG Maker tiles are 48x48 pixels, and sprite Y is at bottom of tile
                const eventY = sprite.y - ($gameMap.tileHeight() / 2);

                console.log(`EventLaserLines: Event ${eventId} sprite position: (${eventX}, ${eventY})`);

                // Create laser for each notetag
                notetags.forEach((notetag, index) => {
                    console.log(`EventLaserLines: Creating laser ${index + 1} for event ${eventId}:`, notetag);

                    try {
                        const laser = new LaserLine(
                            eventX,
                            eventY,
                            notetag.settings.direction,
                            notetag.settings.length,
                            notetag.settings
                        );

                        // Position laser at event location
                        laser.x = eventX;
                        laser.y = eventY;

                        // Add to active lasers and layer
                        this.activeLasers.push(laser);
                        this.laserLayer.addChild(laser);

                        console.log(`EventLaserLines: Successfully created laser ${notetag.laserId} at (${eventX}, ${eventY})`);
                        console.log('EventLaserLines: Laser properties:', {
                            x: laser.x,
                            y: laser.y,
                            width: laser.width,
                            height: laser.height,
                            visible: laser.visible,
                            opacity: laser.opacity,
                            parent: laser.parent
                        });
                    } catch (error) {
                        console.error(`EventLaserLines: Error creating laser ${notetag.laserId}:`, error);
                    }
                });
            });

            console.log('EventLaserLines: Update complete. Total active lasers:', this.activeLasers.length);
            console.log('EventLaserLines: Laser layer children:', this.laserLayer.children.length);

            // Reset the update flag
            this.needsUpdate = false;
        }

        // Force an update on next frame (call this when events change)
        requestUpdate() {
            this.needsUpdate = true;
        }

        // Update method that should be called every frame (but only updates when needed)
        update() {
            this.updateLasers();
        }

        // Debug method to create a test laser
        createTestLaser() {
            console.log('EventLaserLines: Creating test laser...');
            try {
                const testLaser = new LaserLine(
                    400, // x position
                    300, // y position
                    'right', // direction
                    200, // length
                    {
                        color: '#FF0066',
                        width: 5,
                        opacity: 255,
                        pulse: false,
                        glow: true
                    }
                );

                this.activeLasers.push(testLaser);
                this.laserLayer.addChild(testLaser);

                console.log('EventLaserLines: Test laser created successfully:', testLaser);
                console.log('EventLaserLines: Test laser parent:', testLaser.parent);
                console.log('EventLaserLines: Laser layer children count:', this.laserLayer.children.length);
            } catch (error) {
                console.error('EventLaserLines: Error creating test laser:', error);
            }
        }

        destroy() {
            // Clean up active lasers
            this.activeLasers.forEach(laser => {
                if (laser.parent) {
                    laser.parent.removeChild(laser);
                }
            });
            this.activeLasers = [];
            this.laserEvents.clear();
            
            // Clean up laser layer
            if (this.laserLayer && this.laserLayer.parent) {
                this.laserLayer.parent.removeChild(this.laserLayer);
            }
            if (this.laserLayer) {
                this.laserLayer.destroy();
            }
        }
    }

    //=============================================================================
    // Scene_Map Integration
    //=============================================================================
    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);
        this._laserManager = new LaserManager(this);
        this._laserManager.initialize();
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);
        if (this._laserManager) {
            this._laserManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        if (this._laserManager) {
            this._laserManager.destroy();
        }
        _Scene_Map_terminate.call(this);
    };

    // Hook into map refresh to update lasers when needed
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);
        if (SceneManager._scene && SceneManager._scene._laserManager) {
            SceneManager._scene._laserManager.requestUpdate();
        }
    };

    // Hook into event page changes to update lasers
    const _Game_Event_refresh = Game_Event.prototype.refresh;
    Game_Event.prototype.refresh = function() {
        _Game_Event_refresh.call(this);
        if (SceneManager._scene && SceneManager._scene._laserManager) {
            SceneManager._scene._laserManager.requestUpdate();
        }
    };

    console.log('EventLaserLines: Plugin loaded successfully');

})();

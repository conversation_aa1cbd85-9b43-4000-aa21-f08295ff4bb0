/*:============================================================================
*
* @target MZ
*
* <AUTHOR>
*
* @plugindesc | Rosedale Skill Tree : Version - 1.1.1 | A simple skill tree.
*
* @url http://rosedale-studios.com
*
* @help
* ╔════════════════════════════════════╗
* ║ ()()                                                              ()() ║
* ║ (^.^)                    - Rosedale Studios -                    (^.^) ║
* ║c(")(")                                                          (")(")ↄ║
* ╚════════════════════════════════════╝

*============================================================================
*  Requirements :
*============================================================================

*============================================================================
*  Instructions :
*============================================================================



*============================================================================
*  Terms Of Use :
*============================================================================
*
*   This Plugin may be used commercially, or non commercially. This plugin may
*  be extended upon, and or shared ONLY with permission from it's owner,
*  ZServ. This plugin is the sole property of ZServ, and may not be
*  without his/her permission!.
*
*============================================================================
*  Version History :
*============================================================================

*  ● Version : 0.1.0
*  ● Date : 04/11/2023
*    ★ Beta Release.

* ● Version : 1.1.1
* ● Date : 29/05/2024
*   ✩ Fix - background image did not work.

*============================================================================
*  Contact Me :
*============================================================================

*  If you have questions, about this plugin, or commissioning me, or have
*  a bug to report, please feel free to contact me by any of the below
*  methods.

*  website : https://www.rosedale-studios.com
*  rmw : https://forums.rpgmakerweb.com/index.php?members/chaucer.44456
*  youtube : https://www.youtube.com/channel/UCYA4VU5izmbQvnjMINssshQ/videos
*  email : chaucer(at)rosedale-studios(dot)com
*  discord : https://discord.gg/nexQGb65uP

*============================================================================

* @command start_skill_tree
* @text Start Skill Scene
* @desc Start the tree scene for the actor specified, and with the tree specified.

* @arg actor
* @text Actor
* @desc The actor that the scene will start for.
* @default 1
* @type actor

* @param menuName
* @text Menu Name
* @desc The name of the skill tree in the menu.
* @default Skill Tree
* @type text

* @param inMenu
* @text Enable In Menu
* @desc Should the skill tree be enabled in the menu.
* @default true
* @type boolean

* @param enableSwitch
* @text Enabled Switch
* @desc Skill tree will be enabled in menu only when this switch is on( leave 0 if always enabled ).
* @default 0
* @type switch

* @param treeBackground
* @text Skill Scene Background
* @desc The backgriund Image used for the Skill Tree Scene.
* @default
* @type file
* @dir img/system/
* @require 1

* @param pointsName
* @text Points Name
* @desc The name of the points that are used to purchase skills.
* @default SP
* @type text

* @param pointsPerLevel
* @text Points Per Level
* @desc how many points are gained per level.
* @default 1
* @type number
* @min 1
* @max 10

* @param bonusPointLevels
* @text Bonus Point Levels
* @desc On every level that is divisible by this number, bonus points( x2 ) will be granted( set to 0  to disable ).
* @default 5
* @type number
* @min 0
* @max 10

* @param gridSize
* @text Tree Grid Size
* @desc How large are the tiles in the grid.
* @default 36
* @type number
* @min 32

* @param lockIcon
* @text Lock Icon
* @desc The icon that will be used for the lock icon.
* @default 0
* @type icon

* @param learnedIcon
* @text Learned Icon
* @desc The icon that will be used for the learned icon.
* @default 0
* @type icon

* @param learnSe
* @text Learn Sound Effect
* @desc Specify the sound effect when a skill is learned.
* @default {"name":"","volume":"90","pitch":"100","pan":"0"}
* @type struct<Audio>

* @param learnAnime
* @text Learn Animation
* @desc Specify the animation that will be played when a skill is learned.
* @default 0
* @type animation

* @param cursorSprite
* @text Tree Cursor Sprite
* @desc The sprite used for the cursor in the tree tree.
* @default
* @type file
* @dir img/system/
* @require 1

* @param cursorFlashSpeed
* @text Cursor Flash Speed
* @desc Set the speed that the cursor flashes at.
* @default 0.10
* @type number
* @min 0.01
* @max 6.28
* @decimals 2
* @parent cursorSprite

* @param arrowColors
* @text Arrow Colors
* @desc Specify the arrow colors here.
* @default {"colorEnabled":"#cfb634","outlineEnabled":"#ff7700","colorDisabled":"#666666","outlineDisabled":"#444444"}
* @type struct<Colors>

*/

/*~struct~Colors:

  * @param colorEnabled
  * @text Enabled Color
  * @desc Set the color of arrows pointing to unlocked skills here.
  * @default #cfb634
  * @type text

  * @param outlineEnabled
  * @text Enabled Outline Color
  * @desc Set the color for the outline of arrows pointing to unlocked skills.
  * @default #ff7700
  * @type text


  * @param colorDisabled
  * @text Disabled Color
  * @desc Set the color of arrows pointing to locked skills here.
  * @default #666666
  * @type text

  * @param outlineDisabled
  * @text Disabled Outline Color
  * @desc Set the color for the outline of arrows pointing to locked skills.
  * @default #444444
  * @type text

*/

/*~struct~Audio:

* @param name
* @text Name
* @desc The name of the audio file to play.
* @default
* @type file
* @dir audio/se
* @require 1

* @param volume
* @text Volume
* @desc The volume level of the audio.
* @default 90
* @type number
* @min 0
* @max 100

* @param pitch
* @text Pitch
* @desc The pitch of the audio.
* @default 100
* @type number
* min 50
* @max 150

* @param pan
* @text Pan
* @desc The pan of the audio.
* @default 0
* @type number
* @min -100
* @max 100

*/

//=============================================================================
var Imported = Imported || {};
Imported['Skill Tree'.toUpperCase()] = true;
//=============================================================================
var Chaucer = Chaucer || {};
Chaucer.skillTree = {};
//=============================================================================

//=============================================================================
// Sprite_SkillTree :
//=============================================================================

//=============================================================================
class Sprite_SkillTree extends Sprite {
    // Sprite_SkillTree

    //=============================================================================
    constructor(data, editorMode) {
        // Called on object creation.
        //=============================================================================

        super(new Bitmap(100, 100));
        this.createGrid();
        this.setData(data);
        this.index = 0;

        this._animationTarget = new Sprite();
        this.addChild(this._animationTarget);
        this._needsRedraw = true;
    }

    //=============================================================================
    get index() {
        // return the current index.
        //=============================================================================

        return this._index || 0;
    }

    //=============================================================================
    set index(value) {
        // set the index to the value specified.
        //=============================================================================

        this._index = value;
        this.refreshWindows();
        this._needsRedraw = true;
    }

    //=============================================================================
    createGrid() {
        // create a grid.
        //=============================================================================

        this._grid = new PIXI.Graphics();
        this.addChild(this._grid);
    }

    //=============================================================================
    setData(data) {
        // set the data.
        //=============================================================================

        this._data = data;
        if (data) {
            // this._data.tileSize = ImageManager.iconWidth * 2;
            this._data.tileSize = Chaucer.skillTree.params.gridSize;
            this.resize(data.width * data.tileSize, data.height * data.tileSize);
        }
        this.initializeIndex();
        this._needsRedraw = true;
        this.refresh();
    }

    //=============================================================================
    initializeIndex() {
        // initialize the index to top center( or closest node to it ).
        //=============================================================================

        if (this._data) {
            const { tree, width, height } = this._data;

            for (let y = 0, l = height; y < l; y++) {
                if (!tree[y]) continue;
                for (let x = 0, l2 = width; x < l2; x++) {
                    if (tree[y][x]) {
                        this.index = tree[y][x].id;
                        y = height;
                        x = width;
                        break;
                    }
                }
            }
        }
    }

    //=============================================================================
    resize(width, height) {
        // resize.
        //=============================================================================

        this.bitmap.resize(width, height);
        this.width = width;
        this.height = height;
    }

    //=============================================================================
    refresh() {
        // refresh the tree tree.
        //=============================================================================

        if (this.parent && this._data) {
            this.refreshWindows();
            this.bitmap.clear();
            this.refreshPosition();
            if (this._editorMode) this.drawGrid();
            this.drawConnectors();
            this.drawNodes();
        }
    }

    //=============================================================================
    refreshWindows() {
        // refresh the windows.
        //=============================================================================

        if (this.parent) {
            const node = this.nodeFromId(this.index);
            this.parent._nameWindow.setNode(node);
            this.parent._requirementsWindow.setNode(node);
            let desc = node ? node.description : '';
            if (node && (!desc || desc.trim() === '') && node.skillId) {
                const skill = $dataSkills[node.skillId];
                if (skill && skill.description) desc = skill.description;
            }
            this.parent._descriptionWindow.setText(desc);
        }
    }

    //=============================================================================
    drawConnectors() {
        // draw all the connectors between the nodes.
        //=============================================================================

        const { width, height, tileSize, tree } = this._data;

        for (let i = 0, l = height; i < l; i++) {
            const y = height - i - 1;
            if (!tree[y]) continue;

            for (let j = 0, l = width; j < l; j++) {
                const x = j;
                if (!tree[y][x]) continue;
                if (tree[y][x].parents.length == 0) continue;

                this.drawConnections(tree[y][x]);
            }
        }
    }

    //=============================================================================
    allNodes() {
        // return all nodes in the current tree.
        //=============================================================================

        if (!this._data) return [];
        const data = [].concat.apply([], this._data.tree);
        return data.filter(node => !!node);
    }

    //=============================================================================
    nodeFromId(id) {
        // retrun the node from the id provided.
        //=============================================================================

        return this.allNodes().find(node => node.id == id) || null;
    }

    //=============================================================================
    getNodePosition(nodeId) {
        // retrun the position the node is currently at.
        //=============================================================================

        const { width, height, tileSize } = this._data;
        const id = isNaN(nodeId) ? nodeId.id : nodeId;

        const x = (id % width) * tileSize + tileSize / 2;
        const y = Math.floor(id / width) * tileSize + tileSize / 2;

        return new Point(x, y);
    }

    //=============================================================================
    isLearned(node) {
        // return if the node is learned.
        //=============================================================================

        if (!node) return false;

        const actor = this.parent._actor || $gameParty.leader();
        return actor.isLearnedSkill(node.skillId);
    }

    //=============================================================================
    isLocked(node) {
        // retrun if the current node is locked behind another node.
        //=============================================================================

        if (!node) return true;

        const parents = node.parents;

        if (parents.length == 0) return false;

        return parents.every(id => !this.isLearned(this.nodeFromId(id)));
    }

    //=============================================================================
    canLearnNode(node) {
        // return the the current actor is capable of learning the node.
        //=============================================================================

        if (!node) return false;
        const { itemId, parents, level, cost } = node;

        if (this.isLocked(node)) return false;
        if (SceneManager._scene._actor.unusedSp() < cost) return false;
        if (SceneManager._scene._actor.level < level) return false;

        return !this.isLearned(node);
    }

    //=============================================================================
    drawConnections(node) {
        // draw all connections for the node provided.
        //=============================================================================

        const p0 = this.getNodePosition(node);

        const iw = (ImageManager.iconWidth || Window_Base._iconWidth) / 2 + 6;
        const ih = (ImageManager.iconHeight || Window_Base._iconHeight) / 2 + 6;

        const radius = Math.sqrt(iw * iw, ih * ih);
        const locked = this._editorMode ? false : this.isLocked(node);
        const colors = Chaucer.skillTree.params.arrowColors;

        const color0 = locked ? colors.outlineDisabled : colors.outlineEnabled;
        const color1 = locked ? colors.colorDisabled : colors.colorEnabled;

        for (let i = 0, l = node.parents.length; i < l; i++) {
            const parent = this.nodeFromId(node.parents[i]);
            const p1 = this.getNodePosition(node.parents[i]);

            const dist = new Point(p0.x - p1.x, p0.y - p1.y);
            const mag = Math.sqrt(dist.x * dist.x + dist.y * dist.y);
            const uVec = new Point(dist.x / mag, dist.y / mag);
            const width = 6;

            const ox = uVec.x * radius;
            const oy = uVec.y * radius;

            const x0 = p1.x + ox;
            const y0 = p1.y + oy;
            const x1 = p0.x - ox;
            const y1 = p0.y - oy;

            this.bitmap.drawArrow(x0, y0, x1, y1, width, color0);

            const x2 = x0 + uVec.x;
            const y2 = y0 + uVec.y;
            const x3 = x1 - uVec.x * 2;
            const y3 = y1 - uVec.y * 2;

            this.bitmap.drawArrow(x2, y2, x3, y3, width - 2, color1);
        }
    }

    //=============================================================================
    drawNodes() {
        // draw all nodes in the tree.
        //=============================================================================

        if (this._data) {
            const { width, height, tileSize, tree } = this._data;

            for (let i = 0, l = height; i < l; i++) {
                if (!tree[i]) continue;
                for (let j = 0, l2 = width; j < l2; j++) {
                    if (!tree[i][j]) continue;
                    this.drawNode(j * tileSize, i * tileSize, tree[i][j]);
                }
            }
        }
    }

    //=============================================================================
    drawNode(x, y, node) {
        // Definition.
        const src = ImageManager.loadSystem('IconSet');
        const item = $dataSkills[node.skillId];
        const iconIndex = item ? item.iconIndex : 0;
        const iw = ImageManager.iconWidth || Window_Base._iconWidth;
        const ih = ImageManager.iconHeight || Window_Base._iconHeight;
        const ox = (this._data.tileSize - iw) / 2;
        const oy = (this._data.tileSize - ih) / 2;
        const sw = iw;
        const sh = ih;
        const sx = (iconIndex % 16) * sw;
        const sy = Math.floor(iconIndex / 16) * sh;
        const dx = x + ox;
        const dy = y + oy;
        const centerX = dx + iw / 2;
        const centerY = dy + ih / 2;
        const diamondSize = iw + 14; // 1 more px bigger diamond
        const ctx = this.bitmap.context;

        // Draw diamond shape behind node based on state
        ctx.save();
        let diamondColor = '#222';
        let diamondAlpha = 0.7;
        if (!this._editorMode && this.isLearned(node)) {
            diamondColor = '#ffe066'; // gold for learned
            diamondAlpha = 0.85;
        } else if (!this._editorMode && this.canLearnNode(node)) {
            // solid bright teal for available
            diamondColor = '#33ff99';
            diamondAlpha = 0.85;
        }
        ctx.globalAlpha = diamondAlpha;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - diamondSize / 2); // top
        ctx.lineTo(centerX + diamondSize / 2, centerY); // right
        ctx.lineTo(centerX, centerY + diamondSize / 2); // bottom
        ctx.lineTo(centerX - diamondSize / 2, centerY); // left
        ctx.closePath();
        ctx.fillStyle = diamondColor;
        ctx.fill();
        // Gold or grey border based on state
        let borderColor = '#ffe066'; // gold by default
        if (!this._editorMode && this.isLocked(node)) {
            borderColor = '#888'; // grey for locked/inactive
        }
        ctx.globalAlpha = 1.0;
        ctx.lineWidth = 2;
        ctx.strokeStyle = borderColor;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - diamondSize / 2); // top
        ctx.lineTo(centerX + diamondSize / 2, centerY); // right
        ctx.lineTo(centerX, centerY + diamondSize / 2); // bottom
        ctx.lineTo(centerX - diamondSize / 2, centerY); // left
        ctx.closePath();
        ctx.stroke();
        ctx.restore();
        this.bitmap._baseTexture.update();

        this.bitmap.blt(src, sx, sy, sw, sh, dx, dy);

        if (!this._editorMode && this.isLocked(node)) {
            const iconIndex = Chaucer.skillTree.params.lockIcon;
            const sx = (iconIndex % 16) * sw;
            const sy = Math.floor(iconIndex / 16) * sh;
            this.bitmap.blt(src, sx, sy, sw, sh, dx, dy);
        } else if (!this._editorMode && this.isLearned(node)) {
            const iconIndex = Chaucer.skillTree.params.learnedIcon;
            const sx = (iconIndex % 16) * sw;
            const sy = Math.floor(iconIndex / 16) * sh;
            this.bitmap.blt(src, sx, sy, sw, sh, dx, dy);
        }

        // Draw Roman numeral or +##% tag if skill name ends with it
        if (item && item.name) {
            const romanMatch = item.name.match(/\s+(I{1,3}|IV|V|VI{0,3}|IX|X)$/);
            if (romanMatch) {
                const numeral = romanMatch[1];
                const fontSize = 18;
                this.bitmap.fontFace = 'GameFont';
                this.bitmap.fontSize = fontSize;
                // Draw a subtle black shadow for readability
                const numeralX = centerX - this.bitmap.measureTextWidth(numeral) / 2;
                const numeralY = centerY + diamondSize / 2 - fontSize + 2;
                this.bitmap.textColor = '#222';
                this.bitmap.drawText(numeral, numeralX + 1, numeralY + 1, 40, fontSize, 'left');
                this.bitmap.textColor = '#ffffff'; // Force pure white
                this.bitmap.drawText(numeral, numeralX, numeralY, 40, fontSize, 'left');
            } else {
                const percentMatch = item.name.match(/\+(\d+)%$/);
                if (percentMatch) {
                    const percentTag = `+${percentMatch[1]}%`;
                    const fontSize = 18;
                    this.bitmap.fontFace = 'GameFont';
                    this.bitmap.fontSize = fontSize;
                    const tagX = centerX - this.bitmap.measureTextWidth(percentTag) / 2;
                    const tagY = centerY + diamondSize / 2 - fontSize + 2;
                    this.bitmap.textColor = '#222';
                    this.bitmap.drawText(percentTag, tagX + 1, tagY + 1, 40, fontSize, 'left');
                    this.bitmap.textColor = '#ffffff';
                    this.bitmap.drawText(percentTag, tagX, tagY, 40, fontSize, 'left');
                }
            }
        }
    }

    //=============================================================================
    refreshPosition() {
        // refresh the position to be centered.
        //=============================================================================
        const width = this._data.width * this._data.tileSize;
        this.x = (Graphics.boxWidth - this.windowWidth() - width) / 2;
    }

    //=============================================================================
    drawGrid() {
        // draw the grid.
        //=============================================================================

        const { tileSize, width, height } = this._data;

        this._grid.clear();

        if (this._gridId >= 0) {
            const rx = (this._gridId % width) * tileSize;
            const ry = Math.floor(this._gridId / width) * tileSize;
            const rw = tileSize;
            const rh = tileSize;

            this._grid.beginFill(0xffff00, 0.5);
            this._grid.drawRect(rx, ry, rw, rh);
            this._grid.endFill();
        }

        this._grid.lineStyle(2, 0xffffff, 1);

        for (let i = 0, l = width; i <= l; i++) {
            const x0 = i * tileSize;
            const y0 = 0;
            const x1 = x0;
            const y1 = height * tileSize;

            this._grid.moveTo(x0, y0);
            this._grid.lineTo(x1, y1);
        }

        for (let i = 0, l = height; i <= l; i++) {
            const x0 = 0;
            const y0 = i * tileSize;
            const x1 = width * tileSize;
            const y1 = y0;

            this._grid.moveTo(x0, y0);
            this._grid.lineTo(x1, y1);
        }
    }

    //=============================================================================
    currentNode() {
        // return the currently selected node.
        //=============================================================================

        return this.nodeFromId(this.index);
    }

    //=============================================================================
    update() {
        // update the grid.
        super.update();
        if (this._needsRedraw && this._data) {
            this.bitmap.clear();
            if (this._editorMode) this.drawGrid();
            this.drawConnectors();
            this.drawNodes();
            this._needsRedraw = false;
        }
        // Redraw every frame for pulsing effect
        if (this._data) {
            // Emit teal particles from available nodes
            if (!this._editorMode) {
                const availableNodes = this.allNodes().filter(n => this.canLearnNode(n));
                for (const node of availableNodes) {
                    // 2% chance per frame per node to emit a teal particle
                    if (Math.random() < 0.02) {
                        const { x, y } = this.getNodePosition(node);
                        this.emitAvailableNodeParticle(x, y);
                    }
                }
            }
        }
        // Update particles with enhanced physics and spiral movement
        if (this._particles) {
            for (let i = this._particles.length - 1; i >= 0; i--) {
                const p = this._particles[i];
                
                // Apply gravity if present
                if (p._gravity) {
                    p._vy += p._gravity;
                }
                
                // Apply rotation speed if present
                if (p._rotationSpeed) {
                    p.rotation += p._rotationSpeed;
                }
                
                // Normal linear movement
                p.x += p._vx;
                p.y += p._vy;
                
                // Apply scale variation
                if (p._scale !== undefined) {
                    p.scale.x = p._scale;
                    p.scale.y = p._scale;
                }
                
                // Add pulsing effect
                if (p._pulse !== undefined) {
                    p._pulse += 0.2;
                    const pulseFactor = 0.9 + 0.1 * Math.sin(p._pulse);
                    p.opacity = Math.min(255, p.opacity * pulseFactor);
                }
                
                // Add sparkle effect
                if (p._sparkle && Math.random() < 0.1) { // 10% chance per frame to sparkle
                    p.opacity = Math.min(255, p.opacity * 1.2); // Bright flash
                }
                
                // Electric pulsing effect
                if (p._electricPulse !== undefined) {
                    p._electricPulse += 0.4;
                    const pulseFactor = 0.6 + 0.4 * Math.sin(p._electricPulse);
                    p.opacity = Math.min(255, p.opacity * pulseFactor);
                }
                
                // Handle bouncing particles
                if (p._bounce && p._bounceCount < p._maxBounces) {
                    const bounds = { left: 0, right: Graphics.width, top: 0, bottom: Graphics.height };
                    let bounced = false;
                    
                    if (p.x <= bounds.left || p.x >= bounds.right) {
                        p._vx *= -0.7; // Reverse and dampen
                        p.x = Math.max(bounds.left, Math.min(bounds.right, p.x));
                        bounced = true;
                    }
                    if (p.y <= bounds.top || p.y >= bounds.bottom) {
                        p._vy *= -0.7; // Reverse and dampen
                        p.y = Math.max(bounds.top, Math.min(bounds.bottom, p.y));
                        bounced = true;
                    }
                    
                    if (bounced) {
                        p._bounceCount++;
                        // Reduce opacity on bounce
                        p.opacity *= 0.8;
                    }
                }
                
                // Update lifetime and fade
                p._lifetime++;
                p.opacity -= p._fadeRate;
                
                // Remove particle if expired
                if (p._lifetime > p._maxLifetime || p.opacity <= 0) {
                    this.removeChild(p);
                    this._particles.splice(i, 1);
                }
            }
        }
        if (!this.updateAnimation() && !this.parent.isEventRunning()) {
            this.updateMouse();
            this.updateInput();
            this.updateCursor();
        }
        this.updateInterpreter();
    }

    emitAvailableNodeParticle(x, y) {
        // Cap total available node particles
        if (!this._particles) this._particles = [];
        const maxParticles = 12;
        if (this._particles.length >= maxParticles) return;
        // Emit 1 or 2 small teal particles
        const count = 1 + (Math.random() < 0.3 ? 1 : 0);
        for (let i = 0; i < count; i++) {
            const particle = new Sprite();
            const size = 4 + Math.random() * 3; // 4 to 7 px
            particle.bitmap = new Bitmap(size, size);
            particle.bitmap.fillRect(0, 0, size, size, '#33ff99');
            particle.x = x;
            particle.y = y;
            particle.opacity = 200 + Math.random() * 55;
            particle.anchor.x = 0.5;
            particle.anchor.y = 0.5;
            particle.rotation = Math.random() * Math.PI * 2;
            const speed = 0.5 + Math.random() * 1.5; // 0.5 to 2 px/frame
            const angle = Math.random() * Math.PI * 2;
            particle._vx = Math.cos(angle) * speed;
            particle._vy = Math.sin(angle) * speed;
            particle._lifetime = 0;
            particle._maxLifetime = 24 + Math.random() * 24; // 24 to 48 frames
            particle._fadeRate = 4 + Math.random() * 2;
            this.addChild(particle);
            this._particles.push(particle);
        }
    }

    //=============================================================================
    emitLearnBurst(x, y) {
        // Use our enhanced particle burst system when learning a skill
        //=============================================================================
        
        // Add expanding circle effect
        this._createExpandingCircle(x, y);
        
        this.createParticleBurst(x, y);
    }
    
    _createExpandingCircle(x, y) {
        // Create three concentric expanding circles
        this._createSingleCircle(x, y, '#33ff99', 0, 0.1, 0.6);    // Inner circle
        this._createSingleCircle(x, y, '#33ff99', 4, 0.3, 0.9);    // Middle circle  
        this._createSingleCircle(x, y, '#33ff99', 8, 0.5, 1.2);    // Outer circle
    }
    
    _createSingleCircle(x, y, color, delay, startScale, endScale) {
        const circle = new Sprite();
        circle.bitmap = new Bitmap(100, 100);
        circle.x = x;
        circle.y = y;
        circle.opacity = 255;
        circle.anchor.x = 0.5;
        circle.anchor.y = 0.5;
        
        // Draw circle
        const ctx = circle.bitmap.context;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(50, 50, 45, 0, Math.PI * 2);
        ctx.stroke();
        
        // Circle animation
        circle._frame = 0;
        circle._maxFrames = 25; // Slower animation
        circle._startScale = startScale;
        circle._endScale = endScale;
        circle._delay = delay;
        circle.scale.x = circle._startScale;
        circle.scale.y = circle._startScale;
        
        circle.update = function() {
            this._frame++;
            
            // Wait for delay
            if (this._frame <= this._delay) {
                return;
            }
            
            const adjustedFrame = this._frame - this._delay;
            const progress = adjustedFrame / (this._maxFrames - this._delay);
            
            // Expand scale
            const scale = this._startScale + (this._endScale - this._startScale) * progress;
            this.scale.x = scale;
            this.scale.y = scale;
            
            // Fade out
            this.opacity = Math.max(0, 255 * (1 - progress));
            
            if (adjustedFrame >= (this._maxFrames - this._delay)) {
                this.parent.removeChild(this);
            }
        };
        
        this.addChild(circle);
    }

    //=============================================================================
    updateCursor() {
        // update the cursor position.
        //=============================================================================
        const node = this.currentNode();
        if (this._cursor && this._data) {
            const { x, y } = this.getNodePosition(this.index);
            const tileSize = this._data.tileSize;
            const { width, height } = this._cursor;
            const ox = this.x - width / 2;
            const oy = this.y - height / 2;
            this._cursor.position.set(x + ox, y + oy);
            this.updateCursorOpacity();
            // Sync aura
            if (this._cursorAura) {
                this._cursorAura.position.set(x + ox + width / 2, y + oy + height / 2);
                this._cursorAura.visible = this._cursor.visible;
            }
        }
        this._cursor.visible = !!node;
        if (this._cursorAura) this._cursorAura.visible = !!node;
    }

    //=============================================================================
    updateCursorOpacity() {
        // update the opacity of the cursor.
        //=============================================================================
        const speed = Chaucer.skillTree.params.cursorFlashSpeed || 0.1;
        const pi2 = Math.PI * 2;
        const angle = (this._cursor._opacityAngle + speed) % pi2;
        this._cursor.alpha = 0.75 + Math.cos(angle) * 0.25;
        this._cursor._opacityAngle = angle;
        // Animate aura/ripple
        if (this._cursorAuraDraw) {
            const pulse = 1.0 + 0.18 * Math.cos(angle);
            const alpha = 0.18 + 0.12 * Math.abs(Math.sin(angle));
            this._cursorAuraDraw(pulse, alpha);
            this._cursorAura.alpha = alpha;
        }
    }

    //=============================================================================
    updateAnimation() {
        // return if we are updating the animation.
        //=============================================================================

        if (!this._animeSprite) return false;

        if (!this._animeSprite.isPlaying()) {
            const node = this.currentNode();
            this._animeSprite.parent.removeChild(this._animeSprite);
            this.parent.startEvent($dataCommonEvents[node.eventId]);
            this._animeSprite = null;
            this.refresh();
        }

        return true;
    }

    //=============================================================================
    updateInterpreter() {
        // update the interpreter.
        //=============================================================================

        this.parent._interpreter.update();
    }

    //=============================================================================
    mouseOverNode(node) {
        // return if the mode is over a node.
        //=============================================================================

        let { x, y } = this.getNodePosition(node.id);

        const width = ImageManager.iconWidth || Window_Base._iconWidth;
        const height = ImageManager.iconHeight || Window_Base._iconHeight;

        let tx = this._mouseX;
        let ty = this._mouseY;

        x -= width / 2;
        y -= height / 2;
        tx -= this.x;
        ty -= this.y;

        if (this.mouseOverWindows(tx, ty)) return false;

        return tx > x && tx < x + width && ty > y && ty < y + width;
    }

    //=============================================================================
    mouseOverWindows(tx, ty) {
        // return if the mouse is over any window.
        //=============================================================================

        const windows = [
            this.parent._descriptionWindow,
            this.parent._requirementsWindow,
            this.parent._nameWindow,
        ];

        for (let i = 0, l = windows.length; i < l; i++) {
            const window = windows[i];

            if (!window.parent) continue;

            const { x, y } = window.getGlobalPosition(new Point(), true);
            const { width, height } = window;

            if (tx > x && tx < x + width && ty > y && ty < y + height) return true;
        }

        return false;
    }

    //=============================================================================
    updateMouse() {
        // update the mouse hovering over a tile.
        //=============================================================================

        this.updateMousePostion();
        this.updateMouseClick();
        this.updateScrollWheel();
    }

    //=============================================================================
    mouseMoved() {
        // return if the mouse moved.
        //=============================================================================

        return TouchInput.x != this._mouseX || TouchInput.y != this._mouseY;
    }

    //=============================================================================
    updateMousePostion() {
        // update the mouses position.
        //=============================================================================

        const allNodes = this.allNodes();

        if (this.mouseMoved()) {
            for (let i = 0, l = allNodes.length; i < l; i++) {
                const node = allNodes[i];

                if (this.mouseOverNode(node)) {
                    if (this.index != node.id) {
                        SoundManager.playCursor();
                        this.index = node.id;
                    }
                }
            }

            if (TouchInput.x && TouchInput.y) {
                this._mouseX = TouchInput.x;
                this._mouseY = TouchInput.y;
            }
        }
    }

    //=============================================================================
    updateMouseClick() {
        // updateMouseClick.
        //=============================================================================

        if (TouchInput.isTriggered()) {
            const allNodes = this.allNodes();

            for (let i = 0, l = allNodes.length; i < l; i++) {
                if (this.mouseOverNode(allNodes[i])) {
                    this.processOk();
                }
            }
        }
    }

    //=============================================================================
    windowWidth() {
        // return the width of the windows.
        //=============================================================================

        return Graphics.boxWidth / 2;
    }

    //=============================================================================
    scrollPadding() {
        // return the control constraints.
        //=============================================================================

        const padding = this.parent._descriptionWindow.height;

        return padding + Chaucer.skillTree.params.scrollPadding;
    }

    //=============================================================================
    updateScrollWheel() {
        // update scroll wheel.
        //=============================================================================

        const sign = Math.sign(TouchInput.wheelY);
        const scrollSpeed = (TouchInput.wheelY / TouchInput.wheelY) * sign;
        const n = 30;

        const padding = 0; // this.scrollPadding();
        const min = Math.max(this.height - Graphics.boxHeight - padding, 0);
        const max = 0;

        if (scrollSpeed) {
            this.y = (this.y - scrollSpeed * n).clamp(-min, max);
        }
    }

    //=============================================================================
    nodesLeft(size = 1) {
        // return the node directly left of this one.
        //=============================================================================

        const { tree, width, height } = this._data;

        const row = Math.floor(this.index / width);
        const col = this.index % width;

        if (col <= 0) return null;

        const startRow = row - Math.floor(size / 2);
        const endRow = startRow + size;
        const list = [];

        for (let i = startRow, l = endRow; i < l; i++) {
            if (!tree[i]) {
                list.push(null);
            } else {
                list.push(tree[i][col - 1]);
            }
        }

        return list
            .filter(n => !!n)
            .sort((a, b) => {
                const c = Math.floor(a.id / width);
                const d = Math.floor(b.id / width);

                return Math.abs(a - row) - Math.abs(b - row);
            })[0];
    }

    //=============================================================================
    nodesRight(size = 1) {
        // return the node directly right of this one.
        //=============================================================================

        const { tree, width, height } = this._data;

        const row = Math.floor(this.index / width);
        const col = this.index % width;

        if (col >= width - 1) return null;

        const startRow = row - Math.floor(size / 2);
        const endRow = startRow + size;
        const list = [];

        for (let i = startRow, l = endRow; i < l; i++) {
            if (!tree[i]) {
                list.push(null);
            } else {
                list.push(tree[i][col + 1]);
            }
        }

        return list
            .filter(n => !!n)
            .sort((a, b) => {
                const c = Math.floor(a.id / width);
                const d = Math.floor(b.id / width);

                return Math.abs(a - row) - Math.abs(b - row);
            })[0];
    }

    //=============================================================================
    nodesDown(size = 1) {
        // return the node directly below this one.
        //=============================================================================

        const { tree, width, height } = this._data;

        const row = Math.floor(this.index / width);
        const col = this.index % width;

        if (row >= height - 1) return null;

        const startCol = col - Math.floor(size / 2);
        const endCol = startCol + size;
        const list = [];

        for (let i = startCol, l = endCol; i < l; i++) {
            list.push(tree[row + 1][i]);
        }

        return list
            .filter(n => !!n)
            .sort((a, b) => {
                const c = a.id % width;
                const d = b.id % width;

                return Math.abs(a - col) - Math.abs(b - col);
            })[0];
    }

    //=============================================================================
    nodesUp(size = 1) {
        // return the node directly above this one.
        //=============================================================================

        const { tree, width, height } = this._data;

        const row = Math.floor(this.index / width);
        const col = this.index % width;

        if (row <= 0) return null;

        const startCol = col - Math.floor(size / 2);
        const endCol = startCol + size;
        const list = [];

        for (let i = startCol, l = endCol; i < l; i++) {
            list.push(tree[row - 1][i]);
        }

        return list
            .filter(n => !!n)
            .sort((a, b) => {
                const c = a.id % width;
                const d = b.id % width;

                return Math.abs(a - col) - Math.abs(b - col);
            })[0];
    }

    //=============================================================================
    updateInput() {
        // update input from keyboard/controller.
        //=============================================================================

        this.updateUpKey();
        this.updateDownKey();
        this.updateLeftKey();
        this.updateRightKey();
        this.updateOkKey();
    }

    //=============================================================================
    getChildrenNodes(node) {
        // find any children nodes attached to this node.
        //=============================================================================

        const allNodes = this.allNodes();

        return allNodes.filter(n => n.parents.includes(node.id));
    }

    //=============================================================================
    getParentNodes(node) {
        // return all parent nodes.
        //=============================================================================

        return node.parents.map(id => this.nodeFromId(id));
    }

    //=============================================================================
    findNodeAbove(node, list) {
        // find any nodes in the list that are ABOVE the node provided.
        //=============================================================================

        const pos0 = this.getNodePosition(node.id);
        const positions = [];
        let result = null;

        for (let i = 0, l = list.length; i < l; i++) {
            const pos1 = this.getNodePosition(list[i]);
            if (pos0.y - pos1.y > 0) {
                if (!result || (result && pos0.x - pos1.x == 0)) result = list[i];
            }
        }

        return result;
    }

    //=============================================================================
    findNodeBelow(node, list) {
        // find any nodes in the list that are BELOW the node provided.
        //=============================================================================

        const pos0 = this.getNodePosition(node.id);
        const positions = [];
        let result = null;

        for (let i = 0, l = list.length; i < l; i++) {
            const pos1 = this.getNodePosition(list[i]);
            if (pos0.y - pos1.y < 0) {
                if (!result || (result && pos0.x - pos1.x == 0)) result = list[i];
            }
        }

        return result;
    }

    //=============================================================================
    findNodeLeft(node, list) {
        // find any nodes in the list that are too the LEFT the node provided.
        //=============================================================================

        const pos0 = this.getNodePosition(node.id);
        const positions = [];
        let result = null;

        for (let i = 0, l = list.length; i < l; i++) {
            const pos1 = this.getNodePosition(list[i]);
            if (pos0.x - pos1.x > 0) {
                if (!result || (result && pos0.y - pos1.y == 0)) result = list[i];
            }
        }

        return result;
    }

    //=============================================================================
    findNodeRight(node, list) {
        // find any nodes in the list that are too the LEFT the node provided.
        //=============================================================================

        const pos0 = this.getNodePosition(node.id);
        const positions = [];
        let result = null;

        for (let i = 0, l = list.length; i < l; i++) {
            const pos1 = this.getNodePosition(list[i]);
            if (pos0.x - pos1.x < 0) {
                if (!result || (result && pos0.y - pos1.y == 0)) result = list[i];
            }
        }

        return result;
    }

    //=============================================================================
    updateUpKey() {
        // update when up is pressed.
        //=============================================================================

        if (Input.isTriggered('up')) {
            let next = this.nodesUp(1);
            if (!next) {
                let node = this.currentNode();
                next = this.findNodeAbove(node, this.getChildrenNodes(node));
                if (!next) {
                    next = this.findNodeAbove(node, this.getParentNodes(node));
                    if (!next) {
                        next = this.nodesUp(3);
                    }
                }
            }

            if (next && this.index != next.id) {
                this.index = next.id;
                SoundManager.playCursor();
                this.keepNodeInFrame();
            }
        }
    }

    //=============================================================================
    updateDownKey() {
        // update when down is pressed.
        //=============================================================================

        if (Input.isTriggered('down')) {
            let next = this.nodesDown(1);
            if (!next) {
                let node = this.currentNode();
                next = this.findNodeBelow(node, this.getChildrenNodes(node));
                if (!next) {
                    next = this.findNodeBelow(node, this.getParentNodes(node));
                    if (!next) {
                        next = this.nodesDown(3);
                    }
                }
            }

            if (next && this.index != next.id) {
                this.index = next.id;
                SoundManager.playCursor();
                this.keepNodeInFrame();
            }
        }
    }

    //=============================================================================
    updateLeftKey() {
        // update left key.
        //=============================================================================

        if (Input.isTriggered('left')) {
            let next = this.nodesLeft(1);
            if (!next) {
                let node = this.currentNode();
                next = this.findNodeLeft(node, this.getChildrenNodes(node));
                if (!next) {
                    next = this.findNodeLeft(node, this.getParentNodes(node));
                    if (!next) {
                        next = this.nodesLeft(3);
                    }
                }
            }

            if (next && this.index != next.id) {
                this.index = next.id;
                SoundManager.playCursor();
                this.keepNodeInFrame();
            }
        }
    }

    //=============================================================================
    updateRightKey() {
        // update right key press.
        //=============================================================================

        if (Input.isTriggered('right')) {
            let next = this.nodesRight(1);
            if (!next) {
                let node = this.currentNode();
                next = this.findNodeRight(node, this.getChildrenNodes(node));
                if (!next) {
                    next = this.findNodeRight(node, this.getParentNodes(node));
                    if (!next) {
                        next = this.nodesRight(3);
                    }
                }
            }

            if (next && this.index != next.id) {
                this.index = next.id;
                SoundManager.playCursor();
                this.keepNodeInFrame();
            }
        }
    }

    //=============================================================================
    updateOkKey() {
        // update when the ok key is pressed.
        //=============================================================================

        if (Input.isTriggered('ok')) this.processOk();
    }

    //=============================================================================
    processOk() {
        // process when ok is pressed/node is clicked.
        //=============================================================================

        const node = this.currentNode();
        if (this.canLearnNode(node)) {
            this.learnNode(node);
            this.keepNodeInFrame();
        } else if (this.isLearned(node) && this.canUnlearnNode(node)) {
            this.unlearnNode(node);
            this.keepNodeInFrame();
        } else {
            SoundManager.playBuzzer();
        }
    }

    //=============================================================================
    keepNodeInFrame() {
        // ensure the current node is in frame!.
        //=============================================================================

        const position = this.getNodePosition(this.currentNode());
        const { x: ox, y: oy } = this.getGlobalPosition(new Point(), true);
        const tileSize = this._data.tileSize;

        const realY = oy + position.y;

        const padding = 0; //this.scrollPadding();

        const limit = Graphics.boxHeight - padding;
        if (realY + tileSize / 2 > limit) {
            this.y += limit - (realY + tileSize / 2);
        } else if (realY - tileSize < 0) {
            this.y -= realY - tileSize / 2;
        }

        this.y = this.y.clamp(-this.height, 0);
    }

    //=============================================================================
    learnNode(node) {
        // learn the node, and start the event.
        //=============================================================================

        this.parent._actor.learnSkill(node.skillId);
        this.payNodeCost(node);
        this.playLearnAnime(node);
        const { x, y } = this.getNodePosition(node);
        this.emitLearnBurst(x, y);
        AudioManager.playSe(this.learnNodeSe());
        this._needsRedraw = true;
        // Refresh windows to update SP display
        this.refreshWindows();
        // Removed celebratory particle burst for performance
    }

    //=============================================================================
    playLearnAnime(nodeId) {
        // play animation for learning a node.
        //=============================================================================

        const { x, y } = this.getNodePosition(nodeId);
        const animeId = Chaucer.skillTree.params.learnAnime || 0;
        const animation = $dataAnimations[animeId];

        if (animeId) {
            this._animeSprite = animation._isMVAnimation
                ? new Sprite_AnimationMV()
                : new Sprite_Animation();
            this._animationTarget.position.set(x, y);
            if (Utils.RPGMAKER_NAME == 'MZ') {
                this._animeSprite.setup([this._animationTarget], animation, false, 0);
            } else {
                this._animeSprite.setup(this._animationTarget, animation, false, 0);
            }

            this.addChild(this._animeSprite);
        }
    }

    //=============================================================================
    learnNodeSe() {
        // return the learn node sound effect.
        //=============================================================================

        return (
            Chaucer.skillTree.params.learnSe || {
                name: '',
                volume: 90,
                pitch: 100,
                pan: 0,
            }
        );
    }

    //=============================================================================
    payNodeCost(node) {
        // pay cost for node to be learned.
        //=============================================================================

        SceneManager._scene._actor._usedSp += Number(node.cost);
    }

    //=============================================================================
    canUnlearnNode(node) {
        // return true if no learned nodes depend on this node or if there are alternative paths
        // Get all nodes that depend on this node
        const dependentNodes = this.allNodes().filter(
            n => n.parents.includes(node.id) && this.isLearned(n)
        );

        // If no nodes depend on this one, it can be unlearned
        if (dependentNodes.length === 0) return true;

        // For each dependent node, check if there's at least one other path to it
        return dependentNodes.every(dependentNode => {
            // Get all parent nodes of the dependent node
            const parentNodes = dependentNode.parents.map(parentId => this.nodeFromId(parentId));

            // Check if there's at least one other learned parent besides the current node
            return parentNodes.some(parent => parent.id !== node.id && this.isLearned(parent));
        });
    }

    //=============================================================================
    unlearnNode(node) {
        // unlearn the node and refund SP
        this.parent._actor.forgetSkill(node.skillId);
        this.refundNodeCost(node);
        SoundManager.playCancel();
        this._needsRedraw = true;
        this.refresh();
        // Refresh windows to update SP display
        this.refreshWindows();
    }

    refundNodeCost(node) {
        SceneManager._scene._actor._usedSp -= Number(node.cost);
    }

    createParticleBurst(x, y, color = '#ffffff') {
        // Balanced medium-scale burst - visually pleasing and noticeable
        
        const colors = ['#33ff99', '#00ffff', '#40e0d0', '#20b2aa', '#48d1cc', '#ffffff'];
        const particleCount = 24;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = new Sprite();
            const size = 3 + Math.random() * 4; // 3-7px - HALVED size
            particle.bitmap = new Bitmap(size, size);
            
            const particleColor = colors[Math.floor(Math.random() * colors.length)];
            
            // Create balanced particle shape
            const ctx = particle.bitmap.context;
            const center = size / 2;
            
            // Balanced glow effect
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
            gradient.addColorStop(0, this._adjustBrightness(particleColor, 0.3)); // Bright center
            gradient.addColorStop(0.7, particleColor);
            gradient.addColorStop(1, this._adjustBrightness(particleColor, -0.2)); // Dark edge
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center, 0, Math.PI * 2);
            ctx.fill();
            
            particle.x = x;
            particle.y = y;
            particle.opacity = 255;
            particle.anchor.x = 0.5;
            particle.anchor.y = 0.5;
            particle.rotation = Math.random() * Math.PI * 2;
            
            // Balanced outward movement
            const angle = (Math.PI * 2 * i) / particleCount;
            const speed = 4 + Math.random() * 6; // 4-10px/frame - medium speed
            particle._vx = Math.cos(angle) * speed;
            particle._vy = Math.sin(angle) * speed;
            
            // Balanced physics
            particle._gravity = 0.03 + Math.random() * 0.04; // Medium gravity
            particle._rotationSpeed = (Math.random() - 0.5) * 0.3; // Gentle rotation
            particle._bounce = Math.random() < 0.25; // 25% bounce chance
            particle._bounceCount = 0;
            particle._maxBounces = 2;
            
            particle._lifetime = 0;
            particle._maxLifetime = 20 + Math.random() * 15; // 20-35 frames - HALVED duration
            particle._fadeRate = 6 + Math.random() * 4; // Faster fade for shorter life
            
            // Add visual enhancements
            particle._scale = 0.8 + Math.random() * 0.4; // 0.8-1.2 scale variation
            particle._pulse = Math.random() * Math.PI * 2; // Pulsing effect
            particle._sparkle = Math.random() < 0.3; // 30% chance to sparkle
            
            this.addChild(particle);
            if (!this._particles) this._particles = [];
            this._particles.push(particle);
        }
    }
    
    _drawStar(ctx, x, y, radius, color) {
        const spikes = 5;
        const outerRadius = radius;
        const innerRadius = radius * 0.4;
        
        ctx.fillStyle = color;
        ctx.beginPath();
        
        for (let i = 0; i < spikes * 2; i++) {
            const angle = (i * Math.PI) / spikes;
            const r = i % 2 === 0 ? outerRadius : innerRadius;
            const px = x + Math.cos(angle) * r;
            const py = y + Math.sin(angle) * r;
            
            if (i === 0) {
                ctx.moveTo(px, py);
            } else {
                ctx.lineTo(px, py);
            }
        }
        
        ctx.closePath();
        ctx.fill();
    }
    
    _adjustBrightness(color, factor) {
        // Simple brightness adjustment
        const hex = color.replace('#', '');
        const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(factor * 255)));
        const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(factor * 255)));
        const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(factor * 255)));
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
    
    _drawDiamond(bitmap, size, color) {
        const ctx = bitmap._context;
        const center = size / 2;
        ctx.save();
        ctx.translate(center, center);
        ctx.beginPath();
        ctx.moveTo(0, -center);
        ctx.lineTo(center, 0);
        ctx.lineTo(0, center);
        ctx.lineTo(-center, 0);
        ctx.closePath();
        ctx.fillStyle = color;
        ctx.fill();
        ctx.restore();
    }
    
    _drawGradientCircle(bitmap, size, color) {
        const ctx = bitmap._context;
        const center = size / 2;
        const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
        gradient.addColorStop(0, this._adjustBrightness(color, 1.3));
        gradient.addColorStop(0.7, color);
        gradient.addColorStop(1, this._adjustBrightness(color, 0.7));
        
        ctx.save();
        ctx.beginPath();
        ctx.arc(center, center, center, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();
        ctx.restore();
    }
    
    _drawSparkle(bitmap, size, color) {
        const ctx = bitmap._context;
        const center = size / 2;
        const length = center * 0.8;
        
        ctx.save();
        ctx.translate(center, center);
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        
        // Draw cross shape
        ctx.beginPath();
        ctx.moveTo(-length, 0);
        ctx.lineTo(length, 0);
        ctx.moveTo(0, -length);
        ctx.lineTo(0, length);
        ctx.stroke();
        
        // Draw diagonal lines
        ctx.beginPath();
        ctx.moveTo(-length * 0.7, -length * 0.7);
        ctx.lineTo(length * 0.7, length * 0.7);
        ctx.moveTo(-length * 0.7, length * 0.7);
        ctx.lineTo(length * 0.7, -length * 0.7);
        ctx.stroke();
        
        ctx.restore();
    }

    createStarBurst(x, y) {
        // Pick a color (teal, white, or rare color)
        const colors = ['#33ff99', '#ffffff', '#ffe066'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        const burst = new Sprite_StarBurst(x, y, color);
        this.addChild(burst);
    }
}

//=============================================================================
window.Sprite_SkillTree = Sprite_SkillTree;
//=============================================================================

//=============================================================================
// Window_SkillName :
//=============================================================================

//=============================================================================
class Window_SkillName extends Window_Base {
    // Window_SkillName

    //=============================================================================
    constructor(rectangle) {
        // Called on object creation.
        //=============================================================================

        super(rectangle);
    }

    //=============================================================================
    setNode(node) {
        // set the current node to read requirements.
        //=============================================================================

        this._node = node;
        this.refresh();
    }

    //=============================================================================
    refresh() {
        // r3efresh the window.
        //=============================================================================

        this.contents.clear();
        const x = 8;
        const y = 0;
        const width = this.contentsWidth() - x * 2;

        this.drawNode(this._node, x, y, width);
    }

    //=============================================================================
    drawNode(node, x, y, width) {
        // draw the node at the position specified.
        //=============================================================================

        if (!node) return;

        const { skillId } = node;
        const item = $dataSkills[skillId];
        const name = item ? item.name : '';
        const iconIndex = item ? item.iconIndex : 0;

        const tWidth = this.textWidth(name);
        // Add text shadow for readability
        const ctx = this.contents.context;
        ctx.save();
        ctx.shadowColor = 'black';
        ctx.shadowBlur = 12;
        this.drawItemName(item, x, y, width, this.lineHeight());
        ctx.restore();

        // this.drawTextEx( name, x, y, width, 'right' );
        //
        // const src = ImageManager.loadSystem( 'IconSet' );
        //
        // const dx = x + width - tWidth - ImageManager.iconWidth - 8;
        // const dy = y ;
        //
        // this.drawIcon( iconIndex, dx, dy );
    }
}

//=============================================================================
window.Window_SkillName = Window_SkillName;
//=============================================================================

//=============================================================================
class Window_SkillRequirements extends Window_Base {
    // Window_SkillRequirements

    //=============================================================================
    constructor(rectangle) {
        // Called on object creation.
        //=============================================================================

        super(rectangle);
    }

    //=============================================================================
    setNode(node) {
        // set the current node to read requirements.
        //=============================================================================

        this._node = node;
        this.refresh();
    }

    //=============================================================================
    refresh() {
        // r3efresh the window.
        //=============================================================================

        this.contents.clear();
        this.drawRequirements();
    }

    //=============================================================================
    drawRequirements() {
        // draw the requirements for the window.
        //=============================================================================

        if (this._node) {
            // Add text shadow for requirements
            const ctx = this.contents.context;
            ctx.save();
            ctx.shadowColor = 'black';
            ctx.shadowBlur = 12;
            const index = this.drawLevelRequirements(0);
            this.drawPointsRequirement(index);
            ctx.restore();
        }
    }

    //=============================================================================
    drawLevelRequirements(index) {
        // draw the item at the position specified.
        //=============================================================================

        if (this._node.level) {
            const actor = SceneManager._scene._actor;
            const current = actor ? actor.level : 0;
            const required = this._node.level;
            const color = current < required ? 18 : 0;
            const text0 = `Required Level : `;
            const text1 = `${current}`;
            const text2 = `/${required}`;
            const width0 = this.textWidth(text0);
            const width1 = this.textWidth(text1);
            const width2 = this.textWidth(text2);
            const x = 8;
            const y = 0;

            this.drawTextEx(text0, x, y, width0, 'right');
            this.drawTextEx(`\\C[${color}]` + text1, x + width0, y, width1, 'right');
            this.drawTextEx(text2, x + width0 + width1, y, width2, 'right');

            return index + 1;
        }

        return index;
    }

    //=============================================================================
    drawPointsRequirement(index) {
        // draw the cost of currency.
        //=============================================================================

        if (this._node.cost) {
            const x = 8;
            const y = this.lineHeight() * index;
            const actor = SceneManager._scene._actor;
            const width = this.contentsWidth() - x * 2;
            const current = actor ? actor.unusedSp() : 0;
            const required = this._node.cost;
            const points = Chaucer.skillTree.params.pointsName;
            const text0 = `Required ${points} : `;
            const text1 = String(current);
            const text2 = '/' + String(required);
            const width0 = this.textWidth(text0);
            const width1 = this.textWidth(text1);
            const width2 = this.textWidth(text2);
            const color = current < required ? 18 : 0;
            this.drawTextEx(text0, x, y, width0, 'right');
            this.drawTextEx(`\\C[${color}]` + text1, x + width0, y, width1, 'right');
            this.drawTextEx(text2, x + width0 + width1, y, width2, 'right');
        }
    }
}

//=============================================================================
window.Window_SkillRequirements = Window_SkillRequirements;
//=============================================================================

//=============================================================================
// Window_SkillDescription :
//=============================================================================

//=============================================================================
class Window_SkillDescription extends Window_Base {
    // Window_SkillDescription

    //=============================================================================
    constructor(rectangle) {
        // Called on object creation.
        //=============================================================================

        super(rectangle);
    }

    //=============================================================================
    setText(text) {
        // set the current text.
        // Split into lines, prepend \fs[20] to each line if not present
        const lines = text
            .split('\n')
            .map(line => (/^\\fs\[\d+\]/.test(line) ? line : '\\fs[20]' + line));
        this._text = lines.join('\n');
        this.refresh();
    }

    //=============================================================================
    refresh() {
        // refresh the window.
        //=============================================================================

        this.contents.clear();
        this.drawDescription();
    }

    //=============================================================================
    drawDescription() {
        // draw the current text.
        const strings = this._text.split('\n');

        this.contents.clear();

        const width = this.contentsWidth() - 8 * 2;
        const ctx = this.contents.context;
        ctx.save();
        ctx.shadowColor = 'black';
        ctx.shadowBlur = 12;

        for (let i = 0, l = strings.length; i < l; i++) {
            const x = 8;
            const y = this.lineHeight() * i;
            this.drawTextEx(strings[i], x, y, width);
        }

        ctx.restore();
    }
}

//=============================================================================
window.Window_SkillDescription = Window_SkillDescription;
//=============================================================================

//=============================================================================
// Sprite_NodeConnector :
//=============================================================================

//=============================================================================
class Sprite_NodeConnector extends Sprite {
    // Sprite_NodeConnector

    //=============================================================================
    constructor() {
        // Called on object creation.
        //=============================================================================
    }
}

//=============================================================================
window.Sprite_NodeConnector = Sprite_NodeConnector;
//=============================================================================

//=============================================================================
// Scene_SkillTree :
//=============================================================================

//=============================================================================
class Scene_SkillTree extends Scene_MenuBase {
    // Scene_SkillTree

    //=============================================================================
    constructor() {
        // Called on object creation.
        //=============================================================================

        super();
        this.createInterpreter();
    }

    //=============================================================================
    isDisplayActorMenuBackgroundImage() {
        // Check if this scene should display actor background images (VisuStella compatibility).
        //=============================================================================

        // Check if VisuStella MainMenuCore is available and this scene is in the ActorBgMenus list
        if (typeof VisuMZ !== 'undefined' && VisuMZ.MainMenuCore && VisuMZ.MainMenuCore.Settings) {
            const actorBgMenus = VisuMZ.MainMenuCore.Settings.General.ActorBgMenus;
            return actorBgMenus && actorBgMenus.includes('Scene_SkillTree');
        }
        return false;
    }

    //=============================================================================
    calcWindowHeight(numLines, selectable) {
        // return the height of a window with x amount of lines.
        //=============================================================================

        return Window_Base.prototype.fittingHeight(numLines);
    }

    //=============================================================================
    isEventRunning() {
        // return if an event is running.
        //=============================================================================

        return this._interpreter.isRunning();
    }

    //=============================================================================
    startEvent(event) {
        // start a common event.
        //=============================================================================

        if (event) {
            this._interpreter.setup(event.list);
        }
    }

    //=============================================================================
    createInterpreter() {
        // create the interpreter.
        //=============================================================================

        this._interpreter = new Game_Interpreter();
    }

    //=============================================================================
    initialize() {
        // initialize variables.
        //=============================================================================

        super.initialize();
        this._actor = $gameActors.actor($gameParty._menuActorId);
        this._tree = $dataSkillTrees[this._actor ? this._actor._lockedSkillTreeClassId : 0];
        this._editorMode = false;
        this._lastWindowWidth = Graphics.boxWidth;
        this._lastWindowHeight = Graphics.boxHeight;
    }

    //=============================================================================
    updateActor() {
        // update the acor.
        //=============================================================================

        super.updateActor();
        this._tree = $dataSkillTrees[this._actor ? this._actor._lockedSkillTreeClassId : 0];
    }

    //=============================================================================
    create() {
        // create the elements of the scene.
        //=============================================================================

        super.create();
        this.createBackgroundSprite();
        // Defer starfield creation to improve loading time
        this._starfieldDeferred = true;
        this._starfieldFadeIn = 0; // Start invisible
        this.createConstellationManager();
        this.createTreeOverlayWindow();
        this.createSkillTree();
        this.createSkillTreeCursor();
        this.createWindowLayer();
        this.createAllWindows();
        this._skillTree.initializeIndex();
        this._skillTree.refresh();
        this.createInfoWindow();
        this.createPortraitSprite();
        // Mark UI as ready for starfield loading
        this._uiReady = true;
    }

    //=============================================================================
    createBackgroundSprite() {
        // create the background spirte.
        //=============================================================================

        const url = Chaucer.skillTree.params.treeBackground;

        let bitmap = new Bitmap(Graphics.width, Graphics.height);
        const x = 2;
        const y = 2;
        const width = Math.floor(bitmap.width / 2) - 4;
        const height = bitmap.height - 4;
        // bitmap.fillAll( '#1b1d23' );
        bitmap.drawRoundedRect(x, y, width, height, 6, '#adafb3');
        bitmap.drawRoundedRect(x + 1, y + 1, width - 2, height - 2, 6, '#317c7c');
        bitmap.drawRoundedRect(x + 1, y + 1, width - 2, height / 2, 6, '#8c6cc0');
        bitmap.gradientFillRect(
            x + 1,
            y + 1 + 6,
            width - 2,
            height - 2 - 12,
            '#8c6cc0',
            '#317c7c',
            true
        );
        if (url) {
            bitmap = ImageManager.loadSystem(url);
        }

        this._treeBackgroundSprite = new Sprite(bitmap);
        this.addChild(this._treeBackgroundSprite);
        // Add a color matrix filter for hue rotation
        this._treeBackgroundSprite.filters = [new PIXI.filters.ColorMatrixFilter()];
        this._hueRotation = 0;
    }

    //=============================================================================
    createSkillTreeCursor() {
        // create the cursor for the tree tree.
        //=============================================================================
        const filename = Chaucer.skillTree.params.cursorSprite;
        const iconW = ImageManager.iconWidth || Window_Base._iconWidth;
        const iconH = ImageManager.iconHeight || Window_Base._iconHeight;
        const width = Math.round(iconW * 2 + 10);
        const height = Math.round(iconH * 2 + 10);
        // --- Clean, crisp cursor bitmap ---
        const backup = new Bitmap(width, height);
        // --- Fantasy diamond cursor bitmap ---
        const cx = width / 2;
        const cy = height / 2;
        const r = (width - 6) / 2; // Main diamond radius
        // Magical glow + main white diamond
        backup.context.save();
        backup.context.shadowColor = 'rgba(120,80,255,0.32)';
        backup.context.shadowBlur = 10;
        backup.context.globalAlpha = 0.9;
        backup.context.strokeStyle = '#ffffff';
        backup.context.lineWidth = 4;
        backup.context.beginPath();
        backup.context.moveTo(cx, cy - r);
        backup.context.lineTo(cx + r, cy);
        backup.context.lineTo(cx, cy + r);
        backup.context.lineTo(cx - r, cy);
        backup.context.closePath();
        backup.context.stroke();
        backup.context.restore();
        // Inner gold diamond
        const r2 = r - 4;
        backup.context.save();
        backup.context.strokeStyle = '#ffd700';
        backup.context.lineWidth = 2;
        backup.context.beginPath();
        backup.context.moveTo(cx, cy - r2);
        backup.context.lineTo(cx + r2, cy);
        backup.context.lineTo(cx, cy + r2);
        backup.context.lineTo(cx - r2, cy);
        backup.context.closePath();
        backup.context.stroke();
        backup.context.restore();
        // Optional: subtle inner highlight
        const r3 = r2 - 3;
        backup.context.save();
        backup.context.strokeStyle = 'rgba(255,255,255,0.10)';
        backup.context.lineWidth = 1;
        backup.context.beginPath();
        backup.context.moveTo(cx, cy - r3);
        backup.context.lineTo(cx + r3, cy);
        backup.context.lineTo(cx, cy + r3);
        backup.context.lineTo(cx - r3, cy);
        backup.context.closePath();
        backup.context.stroke();
        backup.context.restore();
        const bitmap = filename ? ImageManager.loadSystem(filename) : backup;
        // --- Ripple/Aura sprite ---
        if (this._cursorAura) this.removeChild(this._cursorAura);
        const aura = new Sprite(new Bitmap(width * 2, height * 2));
        aura.anchor.set(0.5, 0.5);
        aura.visible = false;
        this._cursorAura = aura;
        const drawAura = (scale = 1, alpha = 0.25) => {
            const b = aura.bitmap;
            b.clear();
            b.drawRoundedRect(
                b.width / 2 - (width / 2) * scale,
                b.height / 2 - (height / 2) * scale,
                width * scale,
                height * scale,
                10 * scale,
                'rgba(80,200,255,0.18)'
            );
        };
        drawAura();
        this._cursorAuraDraw = drawAura;
        this._cursorAuraScale = 1;
        this._cursorAuraAlpha = 0.25;
        this._cursorAuraPulse = 0;
        // Add aura and cursor as siblings
        this._cursor = new Sprite(bitmap);
        this._cursor.visible = false;
        this._cursor._opacityAngle = 0;
        this._skillTree._cursor = this._cursor;
        this.addChild(aura);
        this.addChild(this._cursor);
    }

    //=============================================================================
    createTreeOverlayWindow() {
        // create a simple semi-transparent window behind the skill tree.
        //=============================================================================

        // Adjust these values to match your skill tree area
        const width = Math.floor(Graphics.width / 2) - 20;
        const height = Graphics.height - 70;
        const x = 10;
        const y = 50;
        const rect = new Rectangle(x, y, width, height);

        this._treeOverlayWindow = new Window_Base(rect);
        this._treeOverlayWindow.setBackgroundType(1); // 1 = transparent, 2 = dim, 0 = normal
        this._treeOverlayWindow.opacity = 0; // Adjust for desired darkness (0-255)
        this._treeOverlayWindow.backOpacity = 0; // Adjust for desired darkness (0-255)
        this._treeOverlayWindow.contents.clear(); // No text, just the window

        this.addChild(this._treeOverlayWindow);
    }

    //=============================================================================
    createSkillTree() {
        // create the sprites for all nodes.
        //=============================================================================

        this._skillTree = new Sprite_SkillTree(this._tree, this._editorMode);

        this.addChild(this._skillTree);
    }

    //=============================================================================
    createAllWindows() {
        // create all windows associated with this scene.
        //=============================================================================

        this.createNameWindow();
        this.createDescriptionWindow();
        this.createRequirementsWindow();
        // super.createAllWindows();
    }

    //=============================================================================
    nameWindowRect() {
        // return rectangle for the name window.
        //=============================================================================
        const height = this.calcWindowHeight(1);
        const width = this._skillTree.windowWidth();
        const x = Graphics.boxWidth - width;
        const y = 60; // Move all info windows down by 60px
        return new Rectangle(x, y, width, height);
    }

    //=============================================================================
    createNameWindow() {
        // create a window that will display the nodes name.
        //=============================================================================

        const rect = this.nameWindowRect();

        this._nameWindow = new Window_SkillName(rect);
        // this._nameWindow.setBackgroundType(1); // Remove or comment out for full transparency
        this._nameWindow.opacity = 0;
        this._nameWindow.backOpacity = 0;
        this.addWindow(this._nameWindow);
    }

    //=============================================================================
    requirementsWindowRect() {
        // return the rectangle for the requirements window.
        //=============================================================================
        let requirementsHeight = Chaucer.skillTree.params.requirementsLineAmount || 2;
        const height = this.calcWindowHeight(requirementsHeight);
        const width = this._skillTree.windowWidth();
        const x = Graphics.boxWidth - width;
        const y = 60 + this.calcWindowHeight(1); // Move down by 60px, below name window
        return new Rectangle(x, y, width, height);
    }

    //=============================================================================
    createRequirementsWindow() {
        // create teh window that displays a nodes cost.
        //=============================================================================

        const rect = this.requirementsWindowRect();

        this._requirementsWindow = new Window_SkillRequirements(rect);
        // this._requirementsWindow.setBackgroundType(1); // Remove or comment out for full transparency
        this._requirementsWindow.opacity = 0;
        this._requirementsWindow.backOpacity = 0;
        this.addWindow(this._requirementsWindow);
    }

    //=============================================================================
    descriptionWindowRect() {
        // return the rectangle for the description window.
        //=============================================================================
        const rect = this.requirementsWindowRect();
        const width = this._skillTree.windowWidth();
        const height = 200; // Fixed height for description window
        const x = Graphics.boxWidth - width;
        const y = rect.y + rect.height; // Directly below requirements window
        return new Rectangle(x, y, width, height);
    }

    //=============================================================================
    descriptionWindowRows() {
        // return how many rows fit in the description window.
        //=============================================================================

        const max = this._descriptionWindow.height;
        let height = 0;
        let n = 0;
        while (height < max) {
            n += 1;
            height = this.calcWindowHeight(n);
        }

        return n - 1;
    }

    //=============================================================================
    createDescriptionWindow() {
        // create the description window.
        //=============================================================================

        const rect = this.descriptionWindowRect();

        this._descriptionWindow = new Window_SkillDescription(rect);
        // this._descriptionWindow.setBackgroundType(1); // Remove or comment out for full transparency
        this._descriptionWindow.opacity = 0;
        this._descriptionWindow.backOpacity = 0;
        this.addWindow(this._descriptionWindow);
    }

    //=============================================================================
    updateLeftClickCancel() {
        // update cancelling of lef tclick.
        //=============================================================================
    }

    //=============================================================================
    update() {
        // update the scene.
        //=============================================================================

        super.update();
        this.updateExit();

        // Create starfield as soon as UI is ready and fade it in gradually
        if (this._starfieldDeferred && this._uiReady) {
            this._starfieldDeferred = false;
            this.createParallaxStarfield();
            // Start starfield containers invisible for fade-in
            if (this._starLayerContainers) {
                for (const container of this._starLayerContainers) {
                    container.alpha = 0;
                }
            }
        }

        // Gradually fade in starfield
        if (this._starLayerContainers && this._starfieldFadeIn < 1) {
            this._starfieldFadeIn += 0.02; // Fade in over ~50 frames (about 0.8 seconds)
            if (this._starfieldFadeIn > 1) this._starfieldFadeIn = 1;
            for (const container of this._starLayerContainers) {
                container.alpha = this._starfieldFadeIn;
            }
        }

        this.refreshInfoWindow();

        // Update portrait positioning if window size changed
        this.updatePortraitPositioning();

        // Update cosmic phenomena
        this.updateCosmicPhenomena();

        if (this._starLayers) {
            for (let i = 0; i < this._starLayers.length; i++) {
                // Frame skip for back layer
                if (!this._starLayerFrameCounter) this._starLayerFrameCounter = [0, 0, 0];
                this._starLayerFrameCounter[i] = (this._starLayerFrameCounter[i] || 0) + 1;
                if (this._starLayerFrameCounter[i] % this._starLayerFrameSkip[i] !== 0) continue;
                for (const star of this._starLayers[i]) star.update();
            }
        }
        if (this._constellationGraphics) {
            this.updateConstellations();
        }
        if (
            this._treeBackgroundSprite &&
            this._treeBackgroundSprite.filters &&
            this._treeBackgroundSprite.filters[0]
        ) {
            this._hueRotation = (this._hueRotation + 0.12) % 360;
            this._treeBackgroundSprite.filters[0].hue(this._hueRotation, false);
        }
    }

    //=============================================================================
    updateExit() {
        // allow exiting the scene via input.
        //=============================================================================

        if (this.isEventRunning()) return false;
        if (this._skillTree._animeSprite) return false;

        // Character switching with Page Up/Page Down (VisuStella Menu framework style)
        if (Input.isTriggered('pageup')) {
            this.previousActor();
        } else if (Input.isTriggered('pagedown')) {
            this.nextActor();
        }

        if (Input.isTriggered('cancel') || TouchInput.isCancelled()) {
            SoundManager.playCancel();
            this.popScene();
        }
    }

    //=============================================================================
    helpAreaText() {
        // Return help text for VisuStella compatibility.
        //=============================================================================

        // Show actor switching controls if there are multiple party members
        if ($gameParty.allMembers().length > 1) {
            return 'Q/W: Switch Ally';
        }
        return '';
    }

    //=============================================================================
    needsPageButtons() {
        // Return true if this scene needs page buttons for VisuStella compatibility.
        //=============================================================================

        return $gameParty.allMembers().length > 1;
    }

    //=============================================================================
    nextActor() {
        // switch to next party member (VisuStella Menu framework style).
        //=============================================================================

        // Use VisuStella's actor selection method if available
        if (typeof $gameParty.makeMenuActorNext === 'function') {
            $gameParty.makeMenuActorNext();
            const newActor = $gameActors.actor($gameParty._menuActorId);
            if (newActor && newActor !== this._actor) {
                this.onActorChange();
            }
            return;
        }

        // Fallback to original method
        const members = $gameParty.allMembers();
        if (members.length <= 1) return;

        const currentIndex = members.indexOf(this._actor);
        const nextIndex = (currentIndex + 1) % members.length;
        const nextActor = members[nextIndex];

        if (nextActor && nextActor !== this._actor) {
            $gameParty._menuActorId = nextActor.actorId();
            this.onActorChange();
        }
    }

    //=============================================================================
    previousActor() {
        // switch to previous party member (VisuStella Menu framework style).
        //=============================================================================

        // Use VisuStella's actor selection method if available
        if (typeof $gameParty.makeMenuActorPrevious === 'function') {
            $gameParty.makeMenuActorPrevious();
            const newActor = $gameActors.actor($gameParty._menuActorId);
            if (newActor && newActor !== this._actor) {
                this.onActorChange();
            }
            return;
        }

        // Fallback to original method
        const members = $gameParty.allMembers();
        if (members.length <= 1) return;

        const currentIndex = members.indexOf(this._actor);
        const prevIndex = (currentIndex - 1 + members.length) % members.length;
        const prevActor = members[prevIndex];

        if (prevActor && prevActor !== this._actor) {
            $gameParty._menuActorId = prevActor.actorId();
            this.onActorChange();
        }
    }

    //=============================================================================
    onActorChange() {
        // handle actor change and refresh scene.
        //=============================================================================

        // Play cursor sound for feedback
        SoundManager.playCursor();

        // Update actor and skill tree data
        this._actor = $gameActors.actor($gameParty._menuActorId);
        this._tree = $dataSkillTrees[this._actor ? this._actor._lockedSkillTreeClassId : 0];

        // Refresh skill tree display
        this._skillTree.setData(this._tree);
        this._skillTree.initializeIndex();
        this._skillTree.refresh();

        // Refresh info window
        this.refreshInfoWindow();

        // Refresh portrait sprite if it exists
        if (this._portraitSprite) {
            this.refreshPortraitSprite();
        }
    }

    //=============================================================================
    createInfoWindow() {
        const width = 170; // About half as wide
        const height = 140; // Further increased height to ensure all lines fit
        const x = Graphics.boxWidth - width - 16;
        const y = Graphics.boxHeight - height - 16;
        const rect = new Rectangle(x, y, width, height);
        this._infoWindow = new Window_Base(rect);
        this._infoWindow.setBackgroundType(1);
        this.addChild(this._infoWindow);
        this.refreshInfoWindow();
    }

    refreshInfoWindow() {
        if (!this._infoWindow) return;
        this._infoWindow.contents.clear();
        const actor = this._actor;
        if (!actor) return;
        const className = actor.currentClass ? actor.currentClass().name : '';
        const pointsName = Chaucer.skillTree.params.pointsName || 'SP';
        const totalPoints = typeof actor.totalSp === 'function' ? actor.totalSp() : 0;
        const unusedPoints = typeof actor.unusedSp === 'function' ? actor.unusedSp() : 0;

        // Show party member indicator (VisuStella style)
        const members = $gameParty.allMembers();
        const currentIndex = members.indexOf(actor);
        const partyIndicator = members.length > 1 ? `(${currentIndex + 1}/${members.length})` : '';

        let y = 0;
        // Name with party indicator (largest, bold, white)
        this._infoWindow.drawTextEx(
            '\\FS[30]\\C[0]' + actor.name() + ' \\FS[20]\\C[8]' + partyIndicator,
            0,
            y,
            150
        );
        y += this._infoWindow.lineHeight();
        // Class (larger, blue)
        this._infoWindow.drawTextEx('\\FS[26]\\C[4]' + className, 0, y, 150);
        y += this._infoWindow.lineHeight();
        // SP (larger, yellow/white)
        this._infoWindow.drawTextEx(
            '\\FS[24]\\C[16]' + pointsName + ': \\C[0]' + unusedPoints + '/' + totalPoints,
            0,
            y,
            150
        );

        // Show character switching hint if multiple party members
        if (members.length > 1) {
            y += this._infoWindow.lineHeight() + 4;
            this._infoWindow.drawTextEx('\\FS[18]\\C[8]PgUp/PgDn: Switch', 0, y, 150);
        }
    }

    //=============================================================================
    createPortraitSprite() {
        // create the portrait sprite for the character.
        //=============================================================================

        const actor = this._actor;
        if (!actor) return;

        const filename = actor.name().toLowerCase() + 'menuart';
        const bitmap = ImageManager.loadPicture(filename);

        this._portraitSprite = new Sprite(bitmap);
        this._portraitSprite.anchor.set(1, 1);
        
        // Position in bottom right corner, avoiding overlap with info window
        // Info window is 170px wide + 16px padding = 186px from right edge
        // Portrait should be positioned to the left of the info window
        // Since anchor is (1,1), we need to account for the portrait width
        this._portraitSprite.x = Graphics.boxWidth - 220; // 220px from right edge
        this._portraitSprite.y = Graphics.boxHeight - 20;
        
        // Scale the portrait to a reasonable size (max 200x200)
        const maxSize = 200;
        if (this._portraitSprite.width > maxSize || this._portraitSprite.height > maxSize) {
            const scale = Math.min(maxSize / this._portraitSprite.width, maxSize / this._portraitSprite.height);
            this._portraitSprite.scale.set(scale, scale);
        }

        this.addChild(this._portraitSprite);
        
        // Add error handling for missing images
        bitmap.addLoadListener(() => {
            if (bitmap.isReady()) {
                // Image loaded successfully, ensure proper scaling
                this._portraitSprite.scale.set(1, 1);
                if (this._portraitSprite.width > maxSize || this._portraitSprite.height > maxSize) {
                    const scale = Math.min(maxSize / this._portraitSprite.width, maxSize / this._portraitSprite.height);
                    this._portraitSprite.scale.set(scale, scale);
                }
                // Make sure sprite is visible after successful load
                this._portraitSprite.visible = true;
            } else {
                // Image failed to load, hide the sprite
                this._portraitSprite.visible = false;
            }
        });
    }

    //=============================================================================
    refreshPortraitSprite() {
        // refresh the portrait sprite for the current character.
        //=============================================================================

        if (!this._portraitSprite) return;
        
        const actor = this._actor;
        if (!actor) return;

        const filename = actor.name().toLowerCase() + 'menuart';
        const bitmap = ImageManager.loadPicture(filename);

        this._portraitSprite.bitmap = bitmap;
        
        // Reset scale and apply new scaling if needed
        this._portraitSprite.scale.set(1, 1);
        const maxSize = 200;
        if (this._portraitSprite.width > maxSize || this._portraitSprite.height > maxSize) {
            const scale = Math.min(maxSize / this._portraitSprite.width, maxSize / this._portraitSprite.height);
            this._portraitSprite.scale.set(scale, scale);
        }
    }

    //=============================================================================
    updatePortraitPositioning() {
        // update portrait positioning if window size changed.
        //=============================================================================

        if (!this._portraitSprite) return;

        // Check if window size has changed
        if (this._lastWindowWidth !== Graphics.boxWidth || this._lastWindowHeight !== Graphics.boxHeight) {
            this._lastWindowWidth = Graphics.boxWidth;
            this._lastWindowHeight = Graphics.boxHeight;

            // Reposition portrait to avoid overlap with info window
            this._portraitSprite.x = Graphics.boxWidth - 220;
            this._portraitSprite.y = Graphics.boxHeight - 20;
        }
    }

    createParallaxStarfield() {
        // Layer configs: [count, minSize, maxSize, minAlpha, maxAlpha, driftSpeed, color, wobble, clusterChance, clusterSizeMin, clusterSizeMax, clusterSpreadMin, clusterSpreadMax]
        const layers = [
            {
                count: 75,
                minSize: 1.0,
                maxSize: 1.9,
                minAlpha: 50,
                maxAlpha: 100,
                drift: 0.015,
                color: 'blue',
                wobble: false,
                clusterChance: 0.3,
                clusterSizeMin: 4,
                clusterSizeMax: 7,
                clusterSpreadMin: 12,
                clusterSpreadMax: 22,
            }, // back - reduced density, softer
            {
                count: 110,
                minSize: 1.7,
                maxSize: 2.6,
                minAlpha: 80,
                maxAlpha: 140,
                drift: 0.06,
                color: 'white',
                wobble: false,
                clusterChance: 0.18,
                clusterSizeMin: 3,
                clusterSizeMax: 5,
                clusterSpreadMin: 14,
                clusterSpreadMax: 26,
            }, // mid - slight increase for depth
            {
                count: 100,
                minSize: 2.1,
                maxSize: 3.4,
                minAlpha: 120,
                maxAlpha: 180,
                drift: 0.11,
                color: 'yellow',
                wobble: true,
                clusterChance: 0.15,
                clusterSizeMin: 2,
                clusterSizeMax: 4,
                clusterSpreadMin: 16,
                clusterSpreadMax: 28,
            }, // front - toned down
        ];
        // Refined cosmic color palette - cooler, more harmonious
        const rareColors = [
            // Cool cosmic tones
            'rgba(255,255,255,1)', // Pure White
            'rgba(176,196,222,1)', // Light Steel Blue
            'rgba(135,206,235,1)', // Sky Blue
            'rgba(230,230,250,1)', // Lavender
            'rgba(240,248,255,1)', // Alice Blue

            // Subtle warm accents
            'rgba(255,215,0,1)', // Gold
            'rgba(255,192,203,1)', // Soft Pink

            // Elemental colors
            'rgba(255,0,0,1)', // Fire (Red)
            'rgba(0,191,255,1)', // Water (Deep Sky Blue)
            'rgba(135,206,235,1)', // Ice (Sky Blue)
            'rgba(144,238,144,1)', // Wind (Light Green)
            'rgba(139,69,19,1)', // Earth (Brown)
            'rgba(255,255,0,1)', // Lightning (Yellow)
            'rgba(255,215,0,1)', // Holy (Gold)
            'rgba(75,0,130,1)', // Shadow (Indigo)
        ];
        const galaxyPositions = [];
        const minGalaxyDist = 800; // Increased from 600 to 800 for better spacing
        this._starLayers = [];
        this._starLayerContainers = [];
        this._starLayerFrameSkip = [3, 1, 1]; // Only update back layer every 3 frames

        // Advanced spatial partitioning system with robust overlap detection
        const SpatialGrid = {
            cellSize: 150, // Smaller cells for better accuracy with large objects
            grid: new Map(),
            getKey: (x, y) => `${Math.floor(x / 150)},${Math.floor(y / 150)}`,
            clear: () => SpatialGrid.grid.clear(),

            // Add object to multiple cells if it spans them
            add: (obj, x, y) => {
                const radius = obj.outerRadius;
                const minX = Math.floor((x - radius) / 150);
                const maxX = Math.floor((x + radius) / 150);
                const minY = Math.floor((y - radius) / 150);
                const maxY = Math.floor((y + radius) / 150);

                for (let gx = minX; gx <= maxX; gx++) {
                    for (let gy = minY; gy <= maxY; gy++) {
                        const key = `${gx},${gy}`;
                        if (!SpatialGrid.grid.has(key)) SpatialGrid.grid.set(key, []);
                        SpatialGrid.grid.get(key).push(obj);
                    }
                }
            },

            // Get all objects in cells that could overlap with the given circle
            getNearby: (x, y, radius) => {
                const nearby = new Set(); // Use Set to avoid duplicates
                const searchRadius = radius * 1.1; // 10% safety margin
                const minX = Math.floor((x - searchRadius) / 150);
                const maxX = Math.floor((x + searchRadius) / 150);
                const minY = Math.floor((y - searchRadius) / 150);
                const maxY = Math.floor((y + searchRadius) / 150);

                for (let gx = minX; gx <= maxX; gx++) {
                    for (let gy = minY; gy <= maxY; gy++) {
                        const key = `${gx},${gy}`;
                        const cell = SpatialGrid.grid.get(key);
                        if (cell) {
                            cell.forEach(obj => nearby.add(obj));
                        }
                    }
                }
                return Array.from(nearby);
            },

            // Robust overlap detection with safety margins
            checkOverlap: (x, y, radius) => {
                const nearby = SpatialGrid.getNearby(x, y, radius);
                return nearby.some(obj => {
                    const dx = obj.x - x;
                    const dy = obj.y - y;
                    const distSquared = dx * dx + dy * dy; // Avoid sqrt for performance
                    const minDistSquared = (obj.outerRadius + radius + 10) ** 2; // +10px safety buffer
                    return distSquared < minDistSquared;
                });
            },

            // Find a valid position using Poisson disk sampling for even distribution
            findValidPosition: (radius, preferredArea = null, maxAttempts = 100) => {
                const margin = radius + 20; // Edge margin
                const minX = margin;
                const maxX = Graphics.width - margin;
                const minY = margin;
                const maxY = Graphics.height - margin;

                // Define minimum separation distance for even distribution
                const minSeparation = radius * 2.5; // 2.5x radius for good spacing

                // Try preferred area first with Poisson-like distribution
                if (preferredArea) {
                    const areaMinX = Math.max(minX, preferredArea.minX);
                    const areaMaxX = Math.min(maxX, preferredArea.maxX);
                    const areaMinY = Math.max(minY, preferredArea.minY);
                    const areaMaxY = Math.min(maxY, preferredArea.maxY);

                    for (let i = 0; i < maxAttempts * 0.7; i++) {
                        const x = areaMinX + Math.random() * (areaMaxX - areaMinX);
                        const y = areaMinY + Math.random() * (areaMaxY - areaMinY);

                        if (
                            !SpatialGrid.checkOverlap(x, y, radius) &&
                            SpatialGrid.checkMinimumSeparation(x, y, minSeparation)
                        ) {
                            return { x, y, success: true };
                        }
                    }
                }

                // Fallback: Grid-based systematic placement for guaranteed distribution
                const gridSize = minSeparation * 0.8; // Slightly smaller for flexibility
                const cols = Math.floor((maxX - minX) / gridSize);
                const rows = Math.floor((maxY - minY) / gridSize);
                const totalCells = cols * rows;

                // Create array of all possible grid positions
                const gridPositions = [];
                for (let row = 0; row < rows; row++) {
                    for (let col = 0; col < cols; col++) {
                        const baseX = minX + col * gridSize;
                        const baseY = minY + row * gridSize;
                        // Add random offset within cell for natural look
                        const x = baseX + Math.random() * gridSize * 0.6 + gridSize * 0.2;
                        const y = baseY + Math.random() * gridSize * 0.6 + gridSize * 0.2;
                        gridPositions.push({ x, y });
                    }
                }

                // Shuffle grid positions for random selection
                for (let i = gridPositions.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [gridPositions[i], gridPositions[j]] = [gridPositions[j], gridPositions[i]];
                }

                // Try grid positions
                for (let i = 0; i < Math.min(gridPositions.length, maxAttempts * 0.3); i++) {
                    const { x, y } = gridPositions[i];
                    if (
                        x >= minX &&
                        x <= maxX &&
                        y >= minY &&
                        y <= maxY &&
                        !SpatialGrid.checkOverlap(x, y, radius)
                    ) {
                        return { x, y, success: true };
                    }
                }

                return { x: 0, y: 0, success: false };
            },

            // Check if position maintains minimum separation from existing objects
            checkMinimumSeparation: (x, y, minDistance) => {
                const nearby = SpatialGrid.getNearby(x, y, minDistance);
                return !nearby.some(obj => {
                    const dx = obj.x - x;
                    const dy = obj.y - y;
                    const distSquared = dx * dx + dy * dy;
                    return distSquared < minDistance * minDistance;
                });
            },

            // Get density score for a region (lower = less crowded, better for placement)
            getDensityScore: (x, y, checkRadius = 300) => {
                const nearby = SpatialGrid.getNearby(x, y, checkRadius);
                let densityScore = 0;

                nearby.forEach(obj => {
                    const dx = obj.x - x;
                    const dy = obj.y - y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    if (dist < checkRadius) {
                        // Closer objects contribute more to density
                        const influence = 1 - dist / checkRadius;
                        densityScore += influence * (obj.outerRadius / 100); // Normalize by size
                    }
                });

                return densityScore;
            },

            // Find position with best distribution (lowest density)
            findBestDistributedPosition: (radius, preferredArea = null, candidates = 50) => {
                const margin = radius + 20;
                const minX = margin;
                const maxX = Graphics.width - margin;
                const minY = margin;
                const maxY = Graphics.height - margin;

                let bestPosition = null;
                let bestScore = Infinity;

                // Generate candidate positions
                for (let i = 0; i < candidates; i++) {
                    let x, y;

                    if (preferredArea && Math.random() < 0.7) {
                        // 70% chance to try preferred area
                        const areaMinX = Math.max(minX, preferredArea.minX);
                        const areaMaxX = Math.min(maxX, preferredArea.maxX);
                        const areaMinY = Math.max(minY, preferredArea.minY);
                        const areaMaxY = Math.min(maxY, preferredArea.maxY);

                        x = areaMinX + Math.random() * (areaMaxX - areaMinX);
                        y = areaMinY + Math.random() * (areaMaxY - areaMinY);
                    } else {
                        // Try anywhere
                        x = minX + Math.random() * (maxX - minX);
                        y = minY + Math.random() * (maxY - minY);
                    }

                    // Skip if overlaps
                    if (SpatialGrid.checkOverlap(x, y, radius)) continue;

                    // Calculate density score (lower is better)
                    const densityScore = SpatialGrid.getDensityScore(x, y);

                    if (densityScore < bestScore) {
                        bestScore = densityScore;
                        bestPosition = { x, y };
                    }
                }

                return bestPosition
                    ? { x: bestPosition.x, y: bestPosition.y, success: true }
                    : { x: 0, y: 0, success: false };
            },
        };
        for (let i = 0; i < layers.length; i++) {
            const layer = layers[i];
            const container = new Sprite();
            const stars = [];
            let j = 0;

            // Clear spatial grid for each layer to prevent cross-layer interference
            SpatialGrid.clear();
            // --- BEGIN: Place a fixed number of super clusters (galaxies) ---
            const minGalaxies = 4,
                maxGalaxies = 5;
            let numGalaxies =
                minGalaxies + Math.floor(Math.random() * (maxGalaxies - minGalaxies + 1));
            let galaxiesPlaced = 0;
            // Track galaxy positions and their max ring radii
            const galaxyData = [];
            while (galaxiesPlaced < numGalaxies && j < layer.count) {
                // Super cluster size: smaller in foreground
                let cluster;
                if (i === 2) {
                    // foreground/front layer
                    cluster = 8 + Math.floor(Math.random() * 9); // 8-16 stars
                } else {
                    cluster = 18 + Math.floor(Math.random() * 28); // 18-45 stars
                }
                // Calculate max ring radius for this galaxy and use for super cluster
                const orbitalCount = 2 + Math.floor(Math.random() * 2); // 2-3 orbital rings (reduced from 2-4)
                const maxRingRadius = 60 + (orbitalCount - 1) * 45;
                // Calculate the true outer radius for this galaxy
                let coreSizeMult = 2.1 + Math.pow(Math.random(), 0.7) * 3.8; // 2.1x–5.9x, reduced from 2.5x-7x (~15% smaller)
                const coreSize = layer.maxSize * coreSizeMult;
                const outerRadius = maxRingRadius + coreSize * 1.5 + 60; // 1.5x core for glow, +60px buffer
                // Use density-aware placement for better distribution
                const preferredArea =
                    Math.random() < 0.65
                        ? {
                              minX: Graphics.width * 0.4,
                              maxX: Graphics.width * 1.0,
                              minY: Graphics.height * 0.4,
                              maxY: Graphics.height * 1.0,
                          }
                        : null;

                // Try density-aware placement first for better distribution
                let result = SpatialGrid.findBestDistributedPosition(
                    outerRadius,
                    preferredArea,
                    60
                );

                // Fallback to regular placement if density-aware fails
                if (!result.success) {
                    result = SpatialGrid.findValidPosition(outerRadius, preferredArea, 40);
                }

                if (!result.success) {
                    numGalaxies--; // Reduce target if can't place
                    continue;
                }
                const cx = result.x;
                const cy = result.y;
                const galaxyObj = { x: cx, y: cy, outerRadius };
                galaxyData.push(galaxyObj);
                SpatialGrid.add(galaxyObj, cx, cy); // Add to spatial grid for fast lookups
                galaxyPositions.push({ x: cx, y: cy });
                galaxiesPlaced++;
                // Core star randomization
                const pastelPalettes = [
                    ['#99ccff', '#33ffe6', '#ffffff', '#8c6cc0'], // blue/teal/white/purple
                    ['#ffe066', '#ffb347', '#fff7ae', '#ff8c42'], // gold/orange/yellow
                    ['#8c6cc0', '#ff66cc', '#ffb3de', '#b266ff'], // purple/magenta/pink
                    ['#7fffd4', '#33ff99', '#baffc9', '#00b894'], // green/mint/lime
                    ['#ff6666', '#ffb347', '#ffd6a5', '#ff8c42'], // red/orange/peach
                    ['#66b3ff', '#c2f0fc', '#b3b3ff', '#99e6e6'], // blue/ice
                    ['#ffb3ba', '#ffdfba', '#ffffba', '#baffc9'], // pastel rainbow
                    ['#adff2f', '#7fff00', '#32cd32', '#00fa9a'], // green/lime/mint
                    ['#ff69b4', '#ffb6c1', '#fff0f5', '#db7093'], // pink/rose/lavender
                    ['#00ced1', '#20b2aa', '#48d1cc', '#40e0d0'], // teal/turquoise
                    ['#ffd700', '#ffec8b', '#fffacd', '#ffa500'], // gold/lemon/cream/orange
                    ['#6ec6ff', '#b388ff', '#ffffff', '#00bcd4'], // sky blue/purple/white/cyan
                ];
                // Replace neonPalettes and neon logic with seasonal and elemental themes
                const seasonalElementalPalettes = [
                    // Spring: pinks, greens, yellows, sky blue
                    ['#ffb7c5', '#baffc9', '#fff7ae', '#aeefff'],
                    // Summer: gold, blue, orange, bright green
                    ['#ffe066', '#6ec6ff', '#ffb347', '#7fff00'],
                    // Fall: orange, red, brown, gold
                    ['#ff8c42', '#ff6666', '#a0522d', '#ffd700'],
                    // Winter: icy blue, white, lavender, pale teal
                    ['#b3b3ff', '#ffffff', '#c2f0fc', '#b388ff'],
                    // Fire: red, orange, gold, yellow
                    ['#ff3c00', '#ffb347', '#ffd700', '#fff700'],
                    // Ice: cyan, blue, white, pale blue
                    ['#00fff7', '#33aaff', '#ffffff', '#b3e0ff'],
                    // Lightning: yellow, white, blue, purple
                    ['#fff700', '#ffffff', '#6ec6ff', '#b388ff'],
                    // Water: blue, teal, aqua, white
                    ['#0099ff', '#00ced1', '#33ffe6', '#ffffff'],
                    // Earth: brown, green, gold, olive
                    ['#ff6500', '#7fff00', '#ffd700', '#556b2f'],
                    // Wind: mint, teal, white, sky blue
                    ['#baffc9', '#00b894', '#ffffff', '#aeefff'],
                    // Holy: gold, white, pale yellow, sky blue
                    ['#ffd700', '#ffffff', '#fff7ae', '#aeefff'],
                    // Shadow: purple, indigo, black, dark blue
                    ['#6c3a5c', '#232946', '#000000', '#3a2e5c'],
                ];
                const useTheme = Math.random() < 0.25; // 1 in 4 chance for a special theme
                const palette = useTheme
                    ? seasonalElementalPalettes[
                          Math.floor(Math.random() * seasonalElementalPalettes.length)
                      ]
                    : pastelPalettes[Math.floor(Math.random() * pastelPalettes.length)];
                const coreColor = palette[0];
                const nebulaColor = palette[1] || palette[0];
                const orbiterColors = palette.slice(1);
                // Increase the randomization ceiling for galaxy scale
                // (coreSizeMult already declared above)
                const coreAlphaBoost = 120 + Math.random() * 60; // much brighter
                const coreWobble = false; // no wobble for super core
                // Cluster drift speed randomization
                const clusterDrift = layer.drift * (0.7 + Math.random() * 0.6); // 0.7x–1.3x
                // Use orbitalCount for ring creation below (do not redeclare)

                // Create orbital system
                const orbitalStars = [];

                // Core star
                const driftDx = 0.08 + Math.random() * 0.08; // always positive, slow rightward drift
                const driftDy = 0; // no vertical drift
                const coreStar = new Sprite_ParallaxStar(
                    layer.maxSize * coreSizeMult,
                    layer.maxSize * (coreSizeMult + 0.5),
                    layer.maxAlpha,
                    layer.maxAlpha,
                    0, // no drift for core itself, handled manually
                    coreColor,
                    coreWobble,
                    cx,
                    cy,
                    true,
                    1.2 + Math.random() * 1.2, // size boost
                    coreAlphaBoost,
                    false,
                    true // isSuperCore
                );
                coreStar._coloredRingIndex = Math.floor(Math.random() * orbitalCount);
                coreStar._coloredRingColor = colorToHexNum(coreStar._color);
                coreStar._isSuperCore = true;
                coreStar._driftDx = driftDx;
                coreStar._driftDy = driftDy;
                coreStar._nebulaColor = nebulaColor;
                coreStar._solarSystemId = j + '_' + Math.floor(Math.random() * 100000);
                coreStar._orbiters = [];
                coreStar._isSolarSystemCore = true;
                coreStar._solarSystemPalette = palette;
                coreStar._solarSystemCenter = { x: cx, y: cy };
                coreStar._solarSystemTime = Math.random() * 1000;
                // --- Add mouse hover pop/enlarge effect (with hitArea) ---
                coreStar.interactive = true;
                // Set a circular hitArea matching the core star's size
                coreStar.hitArea = new PIXI.Circle(
                    coreStar._size,
                    coreStar._size,
                    coreStar._size * 0.6
                );
                coreStar._hoverScale = 1.18;
                coreStar._isHovered = false;
                coreStar.on('pointerover', function () {
                    this._isHovered = true;
                });
                coreStar.on('pointerout', function () {
                    this._isHovered = false;
                });
                // --- MICRO-ORBITERS (DUST) ---
                coreStar._microOrbiters = [];
                let microOrbiterCount = 18;
                if (useTheme) microOrbiterCount = Math.round(microOrbiterCount * 1.5); // 50% more for themed suns
                for (let m = 0; m < microOrbiterCount; m++) {
                    // Weighted random: more close in, fewer far out
                    const t = Math.pow(Math.random(), 2.2); // bias toward 0
                    const minR = coreStar._size * 1.1;
                    const maxR = maxRingRadius * 0.98;
                    const radius = minR + t * (maxR - minR);
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 0.001 + Math.random() * 0.0015;
                    // 2, 3, or 4px dot, 3 is most common
                    let dotSizeRand = Math.random();
                    let dotSize = 3;
                    if (dotSizeRand < 0.15) dotSize = 2;
                    else if (dotSizeRand > 0.85) dotSize = 4;
                    // --- Color variance for micro orbiters ---
                    let dotColor;
                    if (Math.random() < 0.6) {
                        dotColor = coreColor; // 60% chance core color
                    } else if (orbiterColors && Math.random() < 0.7) {
                        dotColor = orbiterColors[Math.floor(Math.random() * orbiterColors.length)];
                    } else if (palette && Math.random() < 0.5) {
                        dotColor = palette[Math.floor(Math.random() * palette.length)];
                    } else {
                        dotColor = rareColors[Math.floor(Math.random() * rareColors.length)];
                    }
                    const dot = new Sprite();
                    dot.bitmap = new Bitmap(dotSize, dotSize);
                    dot.bitmap.fillRect(0, 0, dotSize, dotSize, dotColor);
                    dot.anchor.x = 0.5;
                    dot.anchor.y = 0.5;
                    dot._orbitRadius = radius;
                    dot._orbitAngle = angle;
                    dot._orbitSpeed = speed;
                    dot._orbitCenter = coreStar._solarSystemCenter;
                    dot._isMicroOrbiter = true;
                    dot.alpha = 0.7 + Math.random() * 0.3;
                    // Add random scale for more variety
                    const scale = 0.8 + Math.random() * 0.6; // 0.8 to 1.4
                    dot.scale.x = scale;
                    dot.scale.y = scale;
                    // Ensure no wobble for micro-orbiters
                    dot._orbitWobble = false;
                    // Initial position
                    dot.x = cx + Math.cos(angle) * radius;
                    dot.y = cy + Math.sin(angle) * radius * 0.38;
                    container.addChild(dot);
                    coreStar._microOrbiters.push(dot);
                }
                stars.push(coreStar);
                container.addChild(coreStar);
                j++;

                // --- BEGIN ORBIT RINGS ---
                // Choose a faint color tint for some clusters
                let ringColor = 0xffffff;
                let useTint = Math.random() < 0.28; // ~28% of clusters get a color tint
                if (useTint) {
                    // Pick from palette or soft cosmic colors
                    const tints = [
                        0x99ccff, 0xffe066, 0x8c6cc0, 0x33ffe6, 0xffb347, 0xff66cc, 0xbaffc9,
                    ];
                    if (palette && Math.random() < 0.6) {
                        // Use a palette color (convert #RRGGBB to 0xRRGGBB)
                        const palCol = palette[Math.floor(Math.random() * palette.length)];
                        if (typeof palCol === 'string' && palCol[0] === '#') {
                            ringColor = parseInt('0x' + palCol.slice(1), 16);
                        } else {
                            ringColor = tints[Math.floor(Math.random() * tints.length)];
                        }
                    } else {
                        ringColor = tints[Math.floor(Math.random() * tints.length)];
                    }
                }
                const orbitRingGfx = new PIXI.Graphics();
                for (let ring = 0; ring < orbitalCount; ring++) {
                    const ringRadius = 60 + ring * 45;
                    // Vary thickness and opacity by ring index
                    const t = ring / (orbitalCount - 1 + 0.0001); // 0 (inner) to 1 (outer)
                    const mainAlpha = 0.18 - t * 0.09; // 0.18 (inner) to 0.09 (outer)
                    const glowAlpha = 0.07 - t * 0.03; // 0.07 (inner) to 0.04 (outer)
                    const mainWidth = 2 + t * 2; // 2px (inner) to 4px (outer)
                    const glowWidth = 6 + t * 4; // 6px (inner) to 10px (outer)
                    let ringColor = 0xffffff; // default: white
                    if (ring === coreStar._coloredRingIndex) {
                        ringColor = coreStar._coloredRingColor;
                    }
                    // Glow (thicker, faint)
                    orbitRingGfx.lineStyle(glowWidth, ringColor, glowAlpha);
                    orbitRingGfx.drawEllipse(cx, cy, ringRadius, ringRadius * 0.38);
                    // Main ring (thinner, brighter)
                    orbitRingGfx.lineStyle(mainWidth, ringColor, mainAlpha);
                    orbitRingGfx.drawEllipse(cx, cy, ringRadius, ringRadius * 0.38);
                }
                orbitRingGfx.cacheAsBitmap = true;
                container.addChildAt(
                    orbitRingGfx,
                    Math.max(0, container.children.indexOf(coreStar))
                );
                // --- END ORBIT RINGS ---

                // --- BEGIN HALFWAY RADIAL RINGS ---
                for (let ring = 0; ring < orbitalCount - 1; ring++) {
                    const ringRadiusA = 60 + ring * 45;
                    const ringRadiusB = 60 + (ring + 1) * 45;
                    const halfwayRadius = (ringRadiusA + ringRadiusB) / 2;
                    let ringColor = 0xffffff;
                    if (
                        ring === coreStar._coloredRingIndex ||
                        ring + 1 === coreStar._coloredRingIndex
                    ) {
                        ringColor = coreStar._coloredRingColor;
                    }
                    orbitRingGfx.lineStyle(1, ringColor, 0.07); // Very skinny, faint
                    orbitRingGfx.drawEllipse(cx, cy, halfwayRadius, halfwayRadius * 0.38);
                }
                // --- END HALFWAY RADIAL RINGS ---

                // Add a straight vertical 'axis' line to each galaxy
                const axisGfx = new PIXI.Graphics();
                const axisColor = coreStar._coloredRingColor || 0xffffff;
                // Make the axis line 30% longer than the outer ring
                const axisLen = maxRingRadius * 0.38 * 1.3;
                // Draw glow (thicker, fainter)
                axisGfx.lineStyle(6, axisColor, 0.07);
                axisGfx.moveTo(cx, cy - axisLen);
                axisGfx.lineTo(cx, cy + axisLen);
                // Draw main line (thinner, brighter)
                axisGfx.lineStyle(2, axisColor, 0.18);
                axisGfx.moveTo(cx, cy - axisLen);
                axisGfx.lineTo(cx, cy + axisLen);
                axisGfx.cacheAsBitmap = true;
                container.addChildAt(axisGfx, Math.max(0, container.children.indexOf(coreStar)));

                // Add radial spoke pattern connecting rings
                const spokeCount = 8; // Number of spokes per galaxy
                for (let s = 0; s < spokeCount; s++) {
                    const angle = (s / spokeCount) * Math.PI * 2;
                    for (let ring = 0; ring < orbitalCount; ring++) {
                        const ringRadius = 60 + ring * 45;
                        const spokeLen = 14 + 6 * Math.random(); // Length of the spoke segment
                        // Start/end points for the spoke (crossing the ring)
                        const x0 = cx + Math.cos(angle) * (ringRadius - spokeLen / 2);
                        const y0 = cy + Math.sin(angle) * (ringRadius - spokeLen / 2) * 0.38;
                        const x1 = cx + Math.cos(angle) * (ringRadius + spokeLen / 2);
                        const y1 = cy + Math.sin(angle) * (ringRadius + spokeLen / 2) * 0.38;
                        axisGfx.lineStyle(2, axisColor, 0.11); // Slightly fainter than axis
                        axisGfx.moveTo(x0, y0);
                        axisGfx.lineTo(x1, y1);
                    }
                }

                // Restore original orbital ring/star creation loop (planets, orbiters, etc.)
                for (let ring = 0; ring < orbitalCount; ring++) {
                    const ringRadius = 60 + ring * 45; // More distance from the core and between rings
                    const starsInRing = 6 + Math.floor(Math.random() * 4); // 6-9 stars per ring
                    const ringPhase = Math.random() * Math.PI * 2; // Random starting phase
                    // Slower orbital speeds with ring-based variation
                    const baseOrbitalSpeed = 0.0025; // Reduced from 0.005 for slower orbits
                    const orbitalSpeed = baseOrbitalSpeed * (1 - ring * 0.15); // Outer rings move slower
                    for (let k = 0; k < starsInRing && j < layer.count; k++, j++) {
                        const angle = (k / starsInRing) * Math.PI * 2 + ringPhase;
                        const x = cx + Math.cos(angle) * ringRadius;
                        const y = cy + Math.sin(angle) * ringRadius;
                        // Vary star properties based on ring
                        const ringColor =
                            orbiterColors[Math.floor(Math.random() * orbiterColors.length)] ||
                            coreColor;
                        const ringSizeMult = 1.7 - ring * 0.18; // was 0.95, now 1.7 for much larger orbiters
                        const ringAlphaBoost = 30 - ring * 10; // Stars get more transparent in outer rings
                        const star = new Sprite_ParallaxStar(
                            layer.minSize * ringSizeMult,
                            layer.maxSize * ringSizeMult,
                            layer.minAlpha + 30,
                            layer.maxAlpha,
                            clusterDrift * (0.9 + Math.random() * 0.2),
                            ringColor,
                            false, // Force no wobble for all orbiters
                            x,
                            y,
                            true,
                            1.8 + Math.random() * 1.2, // was 0.9 + Math.random() * 1.0, now much larger
                            ringAlphaBoost
                        );
                        // Add orbital properties
                        star._isOrbital = true;
                        star._orbitCore = coreStar;
                        star._orbitCenter = coreStar._solarSystemCenter; // always reference core's center
                        star._orbitRadius = ringRadius;
                        star._orbitSpeed = orbitalSpeed * (0.7 + Math.random() * 0.2); // Slight variation per star, slower overall
                        star._orbitPhase = angle;
                        star._orbitWobble = false; // No wobble for solar system orbiters
                        star._wobble = false; // Ensure no visual wobble for orbiters
                        stars.push(star);
                        container.addChild(star);
                    }
                }
                galaxiesPlaced++;
            }
            // --- END: Place a fixed number of super clusters ---
            // Now fill the rest of the stars as regular clusters or singles
            while (j < layer.count) {
                let cluster = 1;
                if (Math.random() < layer.clusterChance && j < layer.count - layer.clusterSizeMin) {
                    cluster =
                        layer.clusterSizeMin +
                        Math.floor(
                            Math.random() * (layer.clusterSizeMax - layer.clusterSizeMin + 1)
                        );
                }

                // Use density-aware placement for regular clusters too
                const clusterRadius = 80; // Estimated radius for regular clusters

                // Try density-aware placement first for better distribution
                let result = SpatialGrid.findBestDistributedPosition(clusterRadius, null, 30);

                // Fallback to regular placement if density-aware fails
                if (!result.success) {
                    result = SpatialGrid.findValidPosition(clusterRadius, null, 20);
                }

                if (!result.success) {
                    j += cluster; // Skip the entire cluster if can't place
                    continue;
                }

                const cx = result.x;
                const cy = result.y;

                // Add this cluster to spatial grid and galaxy data
                const clusterObj = { x: cx, y: cy, outerRadius: clusterRadius };
                galaxyData.push(clusterObj);
                SpatialGrid.add(clusterObj, cx, cy);
                let clusterCoreColor = null,
                    clusterCoreIsRare = false;
                if (cluster > 1 && Math.random() < 0.25) {
                    clusterCoreColor = rareColors[Math.floor(Math.random() * rareColors.length)];
                    clusterCoreIsRare = true;
                }
                for (let k = 0; k < cluster && j < layer.count; k++, j++) {
                    let color,
                        isRare = false,
                        sizeBoost = 0,
                        alphaBoost = 0;
                    // (B) Supergiant stars removed per user preference
                    var isSuperGiant = false; // All stars are now regular (no supergiants)

                    if (k === 0 && clusterCoreColor) {
                        color = clusterCoreColor;
                        isRare = true;
                        sizeBoost = 1.2;
                        alphaBoost = 25; // Reduced from 40 to 25
                    } else if (Math.random() < 1 / 16) {
                        // Reduced from 1/12 to 1/16
                        color = rareColors[Math.floor(Math.random() * rareColors.length)];
                        isRare = true;
                    } else if (layer.color === 'blue') {
                        color = 'rgba(180,200,255,1)';
                    } else if (layer.color === 'yellow') {
                        if (cluster === 1) {
                            color = 'rgba(255,255,255,1)';
                        } else {
                            color = 'rgba(255,255,200,1)';
                        }
                    } else {
                        color = 'rgba(255,255,255,1)';
                    }
                    let x = cx,
                        y = cy;
                    if (cluster > 1) {
                        const angle = Math.random() * Math.PI * 2;
                        const dist =
                            layer.clusterSpreadMin +
                            Math.random() * (layer.clusterSpreadMax - layer.clusterSpreadMin);
                        x += Math.cos(angle) * dist;
                        y += Math.sin(angle) * dist;
                        sizeBoost += 0.5;
                        alphaBoost += 20;
                    }
                    // --- GIANT PLANET LOGIC REMOVED PER USER PREFERENCE ---
                    let isGiantPlanet = false; // No more giant drifter planets
                    const star = new Sprite_ParallaxStar(
                        layer.minSize,
                        layer.maxSize,
                        layer.minAlpha,
                        layer.maxAlpha,
                        layer.drift,
                        color,
                        layer.wobble,
                        x,
                        y,
                        isRare,
                        sizeBoost,
                        alphaBoost,
                        isSuperGiant
                    );
                    if (isGiantPlanet) star._isGiantPlanet = true;
                    // DO NOT set any orbital properties here!
                    stars.push(star);
                    container.addChild(star);
                }
            }
            this._starLayers.push(stars);
            this._starLayerContainers.push(container);
            // Insert starfield behind constellations and UI elements but above background
            // Find the constellation graphics index as reference point
            const constellationIndex = this.children.indexOf(this._constellationGraphics);
            if (constellationIndex >= 0) {
                this.addChildAt(container, constellationIndex);
            } else {
                // Fallback: add after background sprite
                const bgIndex = this.children.indexOf(this._treeBackgroundSprite);
                this.addChildAt(container, bgIndex >= 0 ? bgIndex + 1 : 1);
            }
        }

        // Initialize cosmic phenomena systems
        this.initializeCosmicPhenomena();
    }

    //=============================================================================
    initializeCosmicPhenomena() {
        // Initialize enhanced cosmic effects systems.
        //=============================================================================

        // Nebula cloud system
        this._nebulaClouds = [];
        this._nebulaContainer = new PIXI.Container();
        this.addChildAt(this._nebulaContainer, 1); // Behind stars but above background
        this.createNebulaClouds();

        // Shooting star system
        this._shootingStars = [];
        this._shootingStarContainer = new PIXI.Container();
        this.addChild(this._shootingStarContainer);
        this._nextShootingStarTime = 300 + Math.random() * 600; // 5-15 seconds

        // Variable star system (enhance existing stars)
        this.initializeVariableStars();

        // Binary star system
        this.createBinaryStarSystems();

        // Asteroid belt system
        this.createAsteroidBelts();

        // Constellation tracing system
        this._constellationTracingLines = [];
        this._constellationTracingContainer = new PIXI.Container();
        this.addChildAt(this._constellationTracingContainer, 2); // Above nebulae, below main stars
        this.createConstellationTracing();
    }

    //=============================================================================
    createNebulaClouds() {
        // Create beautiful nebula clouds using radial gradients.
        //=============================================================================
        // Nebula clouds removed per user preference - cleaner space background
        // No clouds created
    }

    //=============================================================================
    initializeVariableStars() {
        // Add variable brightness to existing stars.
        //=============================================================================

        if (!this._starLayers) return;

        for (const layer of this._starLayers) {
            for (const star of layer) {
                // 5% chance for a star to be variable
                if (Math.random() < 0.05) {
                    star._isVariable = true;
                    star._variablePeriod = 120 + Math.random() * 480; // 2-10 seconds
                    star._variableAmplitude = 0.3 + Math.random() * 0.4; // 30-70% brightness variation
                    star._variablePhase = Math.random() * Math.PI * 2;
                    star._variableSpeed = (Math.PI * 2) / star._variablePeriod;
                }
            }
        }
    }

    //=============================================================================
    createBinaryStarSystems() {
        // Create binary star systems with shared orbits.
        //=============================================================================

        if (!this._starLayers) return;

        // Find super cores and convert some to binary systems
        const superCores = [];
        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isSuperCore) {
                    superCores.push(star);
                }
            }
        }

        // Convert 20% of super cores to binary systems
        const binaryCount = Math.floor(superCores.length * 0.2);
        for (let i = 0; i < binaryCount; i++) {
            const primaryStar = superCores[i];
            if (primaryStar._isBinary) continue; // Skip if already binary

            // Create companion star
            const companionStar = new Sprite_ParallaxStar(
                primaryStar._size * 0.6,
                primaryStar._size * 0.8,
                primaryStar._baseAlpha * 0.8,
                primaryStar._baseAlpha,
                0, // No drift for binary companion
                primaryStar._color,
                false, // No wobble
                primaryStar.x + 40,
                primaryStar.y,
                true, // Is rare
                0,
                30, // Size and alpha boost
                false,
                true // Not supergiant, is super core
            );

            // Set up binary orbital mechanics
            primaryStar._isBinary = true;
            primaryStar._binaryCompanion = companionStar;
            primaryStar._binaryDistance = 35 + Math.random() * 25; // 35-60px apart
            primaryStar._binarySpeed = 0.005 + Math.random() * 0.003; // Slow orbit
            primaryStar._binaryPhase = Math.random() * Math.PI * 2;

            companionStar._isBinary = true;
            companionStar._binaryPrimary = primaryStar;
            companionStar._binaryDistance = primaryStar._binaryDistance;
            companionStar._binarySpeed = primaryStar._binarySpeed;
            companionStar._binaryPhase = primaryStar._binaryPhase + Math.PI; // Opposite side

            // Add companion to the same container as primary
            const container = primaryStar.parent;
            if (container) {
                container.addChild(companionStar);
            }
        }
    }

    //=============================================================================
    createAsteroidBelts() {
        // Create asteroid belts around some solar systems.
        //=============================================================================

        if (!this._starLayers) return;

        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isSuperCore && Math.random() < 0.3) {
                    // 30% chance for asteroid belt
                    const beltRadius = 120 + Math.random() * 80; // Between planet orbits
                    const asteroidCount = 8 + Math.floor(Math.random() * 12); // 8-19 asteroids

                    star._asteroidBelt = [];

                    for (let i = 0; i < asteroidCount; i++) {
                        const asteroid = new Sprite();
                        const size = 1 + Math.random() * 2; // 1-3px
                        asteroid.bitmap = new Bitmap(size, size);
                        asteroid.bitmap.fillRect(0, 0, size, size, 'rgba(150,150,150,0.8)');

                        asteroid.anchor.x = 0.5;
                        asteroid.anchor.y = 0.5;
                        asteroid._orbitRadius = beltRadius + (Math.random() - 0.5) * 20; // Slight variation
                        asteroid._orbitSpeed = 0.001 + Math.random() * 0.0005; // Very slow
                        asteroid._orbitPhase =
                            (i / asteroidCount) * Math.PI * 2 + Math.random() * 0.5;
                        asteroid._orbitCenter = star._solarSystemCenter;
                        asteroid._tumbleSpeed = (Math.random() - 0.5) * 0.1;

                        // Initial position
                        asteroid.x =
                            star._solarSystemCenter.x +
                            Math.cos(asteroid._orbitPhase) * asteroid._orbitRadius;
                        asteroid.y =
                            star._solarSystemCenter.y +
                            Math.sin(asteroid._orbitPhase) * asteroid._orbitRadius * 0.38;

                        star._asteroidBelt.push(asteroid);
                        const container = star.parent;
                        if (container) {
                            container.addChild(asteroid);
                        }
                    }
                }
            }
        }
    }

    //=============================================================================
    createConstellationTracing() {
        // Create faint lines connecting nearby star clusters.
        //=============================================================================

        if (!this._starLayers) return;

        this._tracingGraphics = new PIXI.Graphics();
        this._constellationTracingContainer.addChild(this._tracingGraphics);

        // Find all super cores for constellation tracing
        const cores = [];
        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isSuperCore) {
                    cores.push(star);
                }
            }
        }

        // Create permanent faint lines between nearby cores
        for (let i = 0; i < cores.length; i++) {
            for (let j = i + 1; j < cores.length; j++) {
                const dx = cores[i].x - cores[j].x;
                const dy = cores[i].y - cores[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // Only connect cores that are reasonably close
                if (distance < 200 && Math.random() < 0.3) {
                    // 30% chance for nearby cores
                    this._tracingGraphics.lineStyle(1, 0x666666, 0.15); // Very faint gray lines
                    this._tracingGraphics.moveTo(cores[i].x, cores[i].y);
                    this._tracingGraphics.lineTo(cores[j].x, cores[j].y);
                }
            }
        }
    }

    //=============================================================================
    updateCosmicPhenomena() {
        // Update all cosmic phenomena systems.
        //=============================================================================

        // Nebula cloud updates removed - no nebula clouds present

        // Update shooting stars
        this.updateShootingStars();

        // Update variable stars
        this.updateVariableStars();

        // Update binary star systems
        this.updateBinaryStars();

        // Update asteroid belts
        this.updateAsteroidBelts();
    }

    //=============================================================================
    updateShootingStars() {
        // Create and update shooting stars.
        //=============================================================================

        // Create new shooting star
        this._nextShootingStarTime--;
        if (this._nextShootingStarTime <= 0) {
            this.createShootingStar();
            this._nextShootingStarTime = 300 + Math.random() * 900; // 5-20 seconds
        }

        // Update existing shooting stars
        for (let i = this._shootingStars.length - 1; i >= 0; i--) {
            const star = this._shootingStars[i];
            star.update();

            if (star._isFinished) {
                this._shootingStarContainer.removeChild(star);
                this._shootingStars.splice(i, 1);
            }
        }
    }

    //=============================================================================
    createShootingStar() {
        // Create a single shooting star.
        //=============================================================================

        const shootingStar = new Sprite_ShootingStar();
        this._shootingStars.push(shootingStar);
        this._shootingStarContainer.addChild(shootingStar);
    }

    //=============================================================================
    updateVariableStars() {
        // Update variable star brightness.
        //=============================================================================

        if (!this._starLayers) return;

        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isVariable) {
                    star._variablePhase += star._variableSpeed;
                    const brightness = 1 + Math.sin(star._variablePhase) * star._variableAmplitude;
                    star.opacity = star._baseAlpha * brightness;
                }
            }
        }
    }

    //=============================================================================
    updateBinaryStars() {
        // Update binary star orbital mechanics.
        //=============================================================================

        if (!this._starLayers) return;

        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isBinary && star._binaryCompanion) {
                    // Update binary orbital positions
                    star._binaryPhase += star._binarySpeed;
                    const companion = star._binaryCompanion;

                    // Calculate center of mass (simplified - primary is heavier)
                    const centerX = star._solarSystemCenter.x;
                    const centerY = star._solarSystemCenter.y;

                    // Primary star orbits at smaller radius
                    const primaryRadius = star._binaryDistance * 0.3;
                    star.x = centerX + Math.cos(star._binaryPhase) * primaryRadius;
                    star.y = centerY + Math.sin(star._binaryPhase) * primaryRadius * 0.38;

                    // Companion orbits at larger radius
                    const companionRadius = star._binaryDistance * 0.7;
                    companion.x = centerX + Math.cos(star._binaryPhase + Math.PI) * companionRadius;
                    companion.y =
                        centerY + Math.sin(star._binaryPhase + Math.PI) * companionRadius * 0.38;
                }
            }
        }
    }

    //=============================================================================
    updateAsteroidBelts() {
        // Update asteroid belt orbital mechanics.
        //=============================================================================

        if (!this._starLayers) return;

        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._asteroidBelt) {
                    for (const asteroid of star._asteroidBelt) {
                        asteroid._orbitPhase += asteroid._orbitSpeed;
                        asteroid.rotation += asteroid._tumbleSpeed;

                        asteroid.x =
                            asteroid._orbitCenter.x +
                            Math.cos(asteroid._orbitPhase) * asteroid._orbitRadius;
                        asteroid.y =
                            asteroid._orbitCenter.y +
                            Math.sin(asteroid._orbitPhase) * asteroid._orbitRadius * 0.38;
                    }
                }
            }
        }
    }

    //=============================================================================
    createConstellationManager() {
        this._constellationGraphics = new PIXI.Graphics();
        this._constellationAlpha = 0;
        this._constellationState = 'idle'; // 'fadein', 'hold', 'fadeout'
        this._constellationTimer = 0;
        this._constellationDuration = 0;
        this._constellationLines = [];
        this._constellationNextTime = this._randomConstellationDelay();
        this.addChild(this._constellationGraphics); // Move to topmost layer for visibility
        // Add supercore line graphics right after constellation graphics
        if (!this._supercoreLineGraphics) this._supercoreLineGraphics = new PIXI.Graphics();
        this.addChild(this._supercoreLineGraphics);
    }

    _randomConstellationDelay() {
        return 30 + Math.random() * 60; // 0.5-1.5 seconds at 60fps
    }

    updateConstellations() {
        if (!this._starLayers || this._starLayers.length < 2) return;
        if (this._constellationState === 'idle') {
            this._constellationTimer++;
            if (this._constellationTimer >= this._constellationNextTime) {
                this._constellationTimer = 0;
                this._constellationState = 'fadein';
                this._constellationAlpha = 0;
                this._constellationDuration = 80; // fade in 1.33s (smoother)
                this._constellationLines = this._generateConstellationLines();
                // Remove drift and label logic
            }
        } else if (this._constellationState === 'fadein') {
            this._constellationAlpha += 1 / 80;
            if (this._constellationAlpha >= 0.7) {
                this._constellationAlpha = 0.7;
                this._constellationState = 'hold';
                this._constellationDuration = 120 + Math.random() * 60; // 2-3s
            }
            this._drawConstellationLines();
        } else if (this._constellationState === 'hold') {
            this._constellationDuration--;
            if (this._constellationDuration <= 0) {
                this._constellationState = 'fadeout';
                this._constellationDuration = 80; // fade out 1.33s (smoother)
            }
            this._drawConstellationLines();
        } else if (this._constellationState === 'fadeout') {
            this._constellationAlpha -= 1 / 80;
            if (this._constellationAlpha <= 0) {
                this._constellationAlpha = 0;
                this._constellationState = 'idle';
                this._constellationNextTime = this._randomConstellationDelay();
                this._constellationLines = [];
                this._constellationGraphics.clear();
            } else {
                this._drawConstellationLines();
            }
        }
    }

    _generateConstellationLines() {
        // Gather all suns and orbiters from all star layers
        let points = [];
        let sunColors = new Map(); // Track sun colors
        if (this._starLayers) {
            for (const layer of this._starLayers) {
                for (const star of layer) {
                    if (star._isSuperCore === true) {
                        points.push(star);
                        // Store sun color for reference, always as hex number
                        sunColors.set(star, colorToHexNum(star._color));
                    } else if (star._isOrbital === true) {
                        points.push(star);
                        // Find parent sun and store its color
                        const parentSun = this._findParentSun(star);
                        if (parentSun) {
                            sunColors.set(star, colorToHexNum(parentSun._color));
                        }
                    }
                }
            }
        }
        const stars = points;
        if (!stars || stars.length < 2) return [];
        const totalCount = Math.min(8 + Math.floor(Math.random() * 6), stars.length); // 8-13 points, or all if fewer
        const used = [];
        // Center of the screen
        const centerX = Graphics.width / 2;
        const centerY = Graphics.height / 2;
        // 1. Start with a random point from the closest 20% to center
        const centerDistances = stars.map((s, i) => ({
            i,
            dist: Math.pow(s.x - centerX, 2) + Math.pow(s.y - centerY, 2),
        }));
        centerDistances.sort((a, b) => a.dist - b.dist);
        const topN = Math.max(1, Math.floor(centerDistances.length * 0.2));
        let idx = centerDistances[Math.floor(Math.random() * topN)].i;
        used.push(idx);
        let prevIdx = idx;
        let prevAngle = null;
        // 2. Build the chain/arc backbone
        for (let step = 1; step < totalCount; step++) {
            let bestScore = -Infinity,
                bestIdx = -1;
            for (let i = 0; i < stars.length; i++) {
                if (used.includes(i)) continue;
                // Distance constraint
                const dx = stars[prevIdx].x - stars[i].x;
                const dy = stars[prevIdx].y - stars[i].y;
                const dist = Math.sqrt(dx * dx + dy * dy);
                if (dist < 180) continue; // Too close, skip
                // Angle constraint
                let angleScore = 0;
                if (prevAngle !== null) {
                    const angle = Math.atan2(
                        stars[i].y - stars[prevIdx].y,
                        stars[i].x - stars[prevIdx].x
                    );
                    let dAngle = Math.abs(angle - prevAngle);
                    dAngle = Math.min(dAngle, Math.abs(Math.PI * 2 - dAngle));
                    if (dAngle < Math.PI / 8) angleScore -= 2;
                    else if (dAngle > Math.PI * 0.7) angleScore -= 2;
                    else angleScore += 1.5 - Math.abs(dAngle - Math.PI / 3);
                }
                let distScore = -Math.abs(dist - 90) / 40;
                const centerDist = Math.sqrt(
                    Math.pow(stars[i].x - centerX, 2) + Math.pow(stars[i].y - centerY, 2)
                );
                let centerScore = -centerDist / 120;
                let score = angleScore + distScore + centerScore + Math.random() * 0.5;
                if (score > bestScore) {
                    bestScore = score;
                    bestIdx = i;
                }
            }
            if (bestIdx >= 0) {
                used.push(bestIdx);
                prevAngle = Math.atan2(
                    stars[bestIdx].y - stars[prevIdx].y,
                    stars[bestIdx].x - stars[prevIdx].x
                );
                prevIdx = bestIdx;
            } else {
                break;
            }
        }
        // 3. Build lines for backbone
        let lines = [];
        for (let i = 0; i < used.length - 1; i++) {
            const a = stars[used[i]];
            const b = stars[used[i + 1]];
            lines.push([a.x, a.y, b.x, b.y]);
        }
        // 4. Add 2-4 long branches from random backbone points to far unused stars
        const branchCount = Math.min(2 + Math.floor(Math.random() * 3), stars.length - used.length); // 2-4 branches
        for (let b = 0; b < branchCount; b++) {
            if (used.length < 3) break;
            const branchFrom = 1 + Math.floor(Math.random() * (used.length - 2));
            const unused = [];
            for (let i = 0; i < stars.length; i++) {
                if (used.includes(i)) continue;
                const dx = stars[used[branchFrom]].x - stars[i].x;
                const dy = stars[used[branchFrom]].y - stars[i].y;
                const dist = dx * dx + dy * dy;
                unused.push({ i, dist });
            }
            if (unused.length > 0) {
                unused.sort((a, b) => b.dist - a.dist);
                const topN = Math.max(1, Math.floor(unused.length * 0.2));
                const pick = unused[Math.floor(Math.random() * topN)];
                const a = stars[used[branchFrom]];
                const bStar = stars[pick.i];
                lines.push([a.x, a.y, bStar.x, bStar.y]);
                used.push(pick.i);
            }
        }
        if (Math.random() < 0.15 && used.length > 3) {
            const a = stars[used[0]];
            const b = stars[used[used.length - 1]];
            lines.push([a.x, a.y, b.x, b.y]);
        }
        // Restore point effects: assign anchor, legendary, starburst, etc
        const now = performance.now();
        // Pick legendary and anchor indices
        // Legendary cluster event: 1 in 10 chance to double legendary points
        let legendaryCount = 2 * (Math.random() < 0.8 ? 1 : 2); // 2 or rarely 4
        if (Math.random() < 0.1) {
            // 10% chance
            legendaryCount *= 2; // double the legendary points
        }
        const anchorCount = 1 + Math.floor(Math.random() * 2); // 1 or 2
        const legendaryIndices = [];
        const anchorIndices = [];
        const available = [...used];
        // Pick legendary
        for (let i = 0; i < legendaryCount && available.length > 0; i++) {
            const idx = available.splice(Math.floor(Math.random() * available.length), 1)[0];
            legendaryIndices.push(idx);
        }
        // Pick anchor (can overlap with legendary)
        for (let i = 0; i < anchorCount && used.length > 0; i++) {
            const idx = used[Math.floor(Math.random() * used.length)];
            if (!anchorIndices.includes(idx)) anchorIndices.push(idx);
        }
        // Track constellation count for sigil guarantee
        if (!this._constellationCounter) this._constellationCounter = 0;
        this._constellationCounter++;
        const isSigilGuaranteed = this._constellationCounter % 3 === 0;
        // Assign properties to each point
        this._constellationPoints = used.map((i, idx, arr) => {
            const star = stars[i];
            // Assign a random legendary variety if legendary
            let legendaryVariety = null;
            let isSuperLegendary = false;
            let baseSize;
            if (legendaryIndices.includes(i)) {
                // Super legendary: 20% chance
                if (Math.random() < 0.2) {
                    isSuperLegendary = true;
                }
                // Larger, more variable size for legendary points
                baseSize = 16 + Math.random() * 4;
            } else {
                baseSize = 13 + Math.random() * 3;
            }
            // Determine how many effects this point gets (1-3, or 4 if super legendary)
            let effectCount;
            if (isSuperLegendary) {
                effectCount = 4;
            } else {
                effectCount = Math.random() < 0.4 ? 1 : Math.random() < 0.7 ? 2 : 3;
            }
            // Create array of available effects (0-64), but include 65 (sigil) with 20% chance
            let availableEffects = Array.from({ length: 80 }, (_, i) => i);
            if (
                Math.random() < 0.2 ||
                (isSigilGuaranteed && legendaryIndices.includes(i) && idx === 0)
            )
                availableEffects.push(65);
            // Guarantee at least one sigil in every third constellation
            if (
                isSigilGuaranteed &&
                legendaryIndices.includes(i) &&
                idx === 0 &&
                !availableEffects.includes(65)
            )
                availableEffects.push(65);
            // Randomly select effects
            legendaryVariety = [];
            for (let j = 0; j < effectCount; j++) {
                // If this is the guaranteed sigil point, force one effect to be 65
                if (
                    isSigilGuaranteed &&
                    legendaryIndices.includes(i) &&
                    idx === 0 &&
                    !legendaryVariety.includes(65)
                ) {
                    legendaryVariety.push(65);
                    continue;
                }
                if (availableEffects.length === 0) break;
                const randomIndex = Math.floor(Math.random() * availableEffects.length);
                legendaryVariety.push(availableEffects.splice(randomIndex, 1)[0]);
            }
            return {
                x: star.x,
                y: star.y,
                phase: Math.random() * Math.PI * 2,
                baseSize: baseSize,
                baseAlpha: 0.18 + Math.random() * 0.12,
                isLegendary: legendaryIndices.includes(i),
                isSuperLegendary: isSuperLegendary,
                isAnchor: anchorIndices.includes(i),
                hasStarburst: Math.random() < 0.3,
                sunColor: sunColors.get(star) || 0xffe066,
                legendaryVariety: legendaryVariety,
            };
        });
        return lines;
    }

    // Helper function to find parent sun for an orbiter
    _findParentSun(orbiter) {
        if (!this._starLayers) return null;
        // Find the closest sun to this orbiter
        let closestSun = null;
        let minDist = Infinity;
        for (const layer of this._starLayers) {
            for (const star of layer) {
                if (star._isSuperCore) {
                    const dx = star.x - orbiter.x;
                    const dy = star.y - orbiter.y;
                    const dist = dx * dx + dy * dy;
                    if (dist < minDist) {
                        minDist = dist;
                        closestSun = star;
                    }
                }
            }
        }
        return closestSun;
    }

    _drawConstellationLines() {
        const g = this._constellationGraphics;
        g.clear();
        // Remove drift
        // Pulsing line opacity
        const t = performance.now() / 1000;
        const alphaFade = this._constellationAlpha || 0;
        const linePulse = 0.7 + 0.3 * Math.sin(t * 1.2 + 1.5);
        const mainAlpha = (0.18 + 0.14 * linePulse) * alphaFade;
        const glowAlpha = (0.06 + 0.04 * linePulse) * alphaFade;
        // Main line: bluish, thinner, lower opacity, pulsing
        g.lineStyle(2, 0x99ccff, mainAlpha, 0.5);
        for (const line of this._constellationLines) {
            g.moveTo(line[0], line[1]);
            g.lineTo(line[2], line[3]);
        }
        // Glow line: bluish, thicker, faint, pulsing
        g.lineStyle(4, 0x99ccff, glowAlpha, 0.5);
        for (const line of this._constellationLines) {
            g.moveTo(line[0], line[1]);
            g.lineTo(line[2], line[3]);
        }
        // Draw bluish glow at each bend/point, with pulsing and star core
        if (this._constellationPoints) {
            for (const pt of this._constellationPoints) {
                // Pulsing effect
                const pulse = 0.7 + 0.3 * Math.sin(t * 1.2 + pt.phase);
                // Size variation: anchor stars are larger
                const anchorMult = pt.isAnchor ? 1.7 : 1.0;
                // --- Legendary points get bigger and fancier ---
                let baseSize = pt.baseSize * anchorMult;
                if (pt.isLegendary) baseSize *= 1.55;
                if (pt.isSuperLegendary) baseSize *= 2;
                const glowRadius = baseSize * pulse;
                let glowColor = pt.isLegendary ? pt.sunColor : 0x99ccff;
                let coreColor = pt.isLegendary ? pt.sunColor : 0xffffff;
                const alpha =
                    (pt.isLegendary ? 0.32 : pt.baseAlpha) *
                    (0.7 + 0.3 * Math.sin(t * 1.2 + pt.phase)) *
                    alphaFade;
                // Main glow
                g.beginFill(glowColor, alpha);
                g.drawCircle(pt.x, pt.y, glowRadius);
                g.endFill();
                // Star core: anchor/legendary stars are larger
                let coreRadius =
                    (pt.isAnchor ? 3.5 : 2.2) + (pt.isLegendary ? 2.2 : 0) + Math.random() * 0.7;
                if (pt.isSuperLegendary) coreRadius *= 2;
                g.beginFill(coreColor, 0.92 * alphaFade);
                g.drawCircle(pt.x, pt.y, coreRadius);
                g.endFill();
                // --- Fancy legendary varieties ---
                if (pt.isLegendary) {
                    // Draw each effect in the legendaryVariety array
                    for (const v of pt.legendaryVariety) {
                        // --- Legendary reticle variations ---
                        // For each effect, scale all size/radius variables by 3 if super legendary
                        if (v === 0) {
                            // Pulsing Multi-Color Ring
                            let ringRadius = glowRadius + 6 + 2 * Math.sin(t * 2 + pt.phase);
                            if (pt.isSuperLegendary) ringRadius *= 3;
                            for (let seg = 0; seg < 24; seg++) {
                                const angle0 = (seg / 24) * Math.PI * 2;
                                const angle1 = ((seg + 1) / 24) * Math.PI * 2;
                                const color = PIXI.utils.rgb2hex([
                                    0.5 + 0.5 * Math.sin(t + angle0),
                                    0.5 + 0.5 * Math.sin(t + angle0 + 2),
                                    0.5 + 0.5 * Math.sin(t + angle0 + 4),
                                ]);
                                g.lineStyle(2.5, color, 0.95 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * ringRadius,
                                    pt.y + Math.sin(angle0) * ringRadius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * ringRadius,
                                    pt.y + Math.sin(angle1) * ringRadius
                                );
                            }
                        } else if (v === 1) {
                            // Radiating Spokes
                            const spokeCount = 12;
                            for (let i = 0; i < spokeCount; i++) {
                                const angle = (i / spokeCount) * Math.PI * 2 + t * 0.7;
                                let len = glowRadius + 10 + 6 * Math.abs(Math.sin(t * 2 + i));
                                if (pt.isSuperLegendary) len *= 3;
                                const alpha =
                                    0.7 * alphaFade * (0.7 + 0.3 * Math.abs(Math.sin(t * 2 + i)));
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 2) {
                            // Double Halo
                            let r1 = glowRadius + 4;
                            let r2 = coreRadius + 3.5;
                            let r3 = glowRadius + 10 + 2 * Math.sin(t + pt.phase);
                            if (pt.isSuperLegendary) {
                                r1 *= 3;
                                r2 *= 3;
                                r3 *= 3;
                            }
                            g.lineStyle(2.5, glowColor, 0.85 * alphaFade);
                            g.drawCircle(pt.x, pt.y, r1);
                            g.lineStyle(1.2, 0xffffff, 0.7 * alphaFade);
                            g.drawCircle(pt.x, pt.y, r2);
                            g.lineStyle(1.5, 0xffe066, 0.5 * alphaFade);
                            g.drawCircle(pt.x, pt.y, r3);
                        } else if (v === 3) {
                            // Animated Dotted Halo
                            const dotCount = 16;
                            let ringR = glowRadius + 10;
                            if (pt.isSuperLegendary) ringR *= 3;
                            for (let i = 0; i < dotCount; i++) {
                                const angle = (i / dotCount) * Math.PI * 2 + t * 0.7;
                                const dx = pt.x + Math.cos(angle) * ringR;
                                const dy = pt.y + Math.sin(angle) * ringR;
                                let dotSize = 2.1 + 1.2 * Math.sin(t * 2 + angle);
                                if (pt.isSuperLegendary) dotSize *= 3;
                                g.beginFill(0xffffff, 0.7 * alphaFade);
                                g.drawCircle(dx, dy, dotSize);
                                g.endFill();
                            }
                        } else if (v === 4) {
                            // Star Flare
                            const armCount = 6;
                            for (let i = 0; i < armCount; i++) {
                                const angle = (i / armCount) * Math.PI * 2;
                                const len = glowRadius + 12 + 6 * Math.abs(Math.sin(t * 2 + i));
                                const alpha =
                                    0.8 * alphaFade * (0.7 + 0.3 * Math.abs(Math.sin(t * 2 + i)));
                                g.lineStyle(3, 0xffffff, alpha);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                            // Cross overlay
                            g.lineStyle(2, 0xffe066, 0.7 * alphaFade);
                            g.moveTo(pt.x - (glowRadius + 8), pt.y);
                            g.lineTo(pt.x + (glowRadius + 8), pt.y);
                            g.moveTo(pt.x, pt.y - (glowRadius + 8));
                            g.lineTo(pt.x, pt.y + (glowRadius + 8));
                        } else if (v === 5) {
                            // Orbiting Mini-Stars
                            const miniCount = 3 + Math.floor(Math.sin(t + pt.phase) * 2);
                            const orbitR = glowRadius + 10;
                            for (let i = 0; i < miniCount; i++) {
                                const angle = (i / miniCount) * Math.PI * 2 + t * 0.9;
                                const dx = pt.x + Math.cos(angle) * orbitR;
                                const dy = pt.y + Math.sin(angle) * orbitR;
                                g.beginFill(0xffe066, 0.85 * alphaFade);
                                g.drawCircle(dx, dy, 2.5 + 1.2 * Math.abs(Math.sin(t * 2 + angle)));
                                g.endFill();
                            }
                        } else if (v === 6) {
                            // Hexagon/Polygonal Frame
                            const sides = 6;
                            const radius = glowRadius + 8 + 2 * Math.sin(t + pt.phase);
                            const rot = t * 0.7;
                            g.lineStyle(2.5, 0xffffff, 0.85 * alphaFade);
                            for (let i = 0; i < sides; i++) {
                                const angle0 = rot + (i / sides) * Math.PI * 2;
                                const angle1 = rot + ((i + 1) / sides) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * radius,
                                    pt.y + Math.sin(angle0) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * radius,
                                    pt.y + Math.sin(angle1) * radius
                                );
                            }
                        } else if (v === 7) {
                            // Pulsing Diamond Frame
                            const size = glowRadius + 8 + 2 * Math.sin(t + pt.phase);
                            const rot = t * 0.5;
                            g.lineStyle(2.5, 0xffffff, 0.85 * alphaFade);
                            for (let i = 0; i < 4; i++) {
                                const angle0 = rot + (i / 4) * Math.PI * 2;
                                const angle1 = rot + ((i + 1) / 4) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * size,
                                    pt.y + Math.sin(angle0) * size
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * size,
                                    pt.y + Math.sin(angle1) * size
                                );
                            }
                        } else if (v === 8) {
                            // Rotating Cross
                            const size = glowRadius + 10;
                            const rot = t * 0.8;
                            g.lineStyle(2.5, 0xffe066, 0.85 * alphaFade);
                            for (let i = 0; i < 4; i++) {
                                const angle = rot + (i / 4) * Math.PI * 2;
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * size,
                                    pt.y + Math.sin(angle) * size
                                );
                            }
                        } else if (v === 9) {
                            // Triple Orbiting Stars
                            const orbitR = glowRadius + 12;
                            for (let i = 0; i < 3; i++) {
                                const angle = (i / 3) * Math.PI * 2 + t * 1.2;
                                const dx = pt.x + Math.cos(angle) * orbitR;
                                const dy = pt.y + Math.sin(angle) * orbitR;
                                g.beginFill(0xffffff, 0.9 * alphaFade);
                                g.drawCircle(dx, dy, 3 + Math.sin(t * 2 + angle));
                                g.endFill();
                            }
                        } else if (v === 10) {
                            // Pulsing Triangle Frame
                            const size = glowRadius + 8 + 2 * Math.sin(t + pt.phase);
                            const rot = t * 0.6;
                            g.lineStyle(2.5, 0xffffff, 0.85 * alphaFade);
                            for (let i = 0; i < 3; i++) {
                                const angle0 = rot + (i / 3) * Math.PI * 2;
                                const angle1 = rot + ((i + 1) / 3) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * size,
                                    pt.y + Math.sin(angle0) * size
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * size,
                                    pt.y + Math.sin(angle1) * size
                                );
                            }
                        } else if (v === 11) {
                            // Double Orbiting Rings
                            const innerR = glowRadius + 6;
                            const outerR = glowRadius + 14;
                            const rot1 = t * 0.7;
                            const rot2 = -t * 0.5;
                            g.lineStyle(2, 0xffffff, 0.7 * alphaFade);
                            g.drawCircle(pt.x, pt.y, innerR);
                            g.lineStyle(1.5, 0xffe066, 0.6 * alphaFade);
                            g.drawCircle(pt.x, pt.y, outerR);
                        } else if (v === 12) {
                            // Pulsing Star Frame
                            const size = glowRadius + 8 + 2 * Math.sin(t + pt.phase);
                            const rot = t * 0.4;
                            g.lineStyle(2.5, 0xffffff, 0.85 * alphaFade);
                            for (let i = 0; i < 5; i++) {
                                const angle0 = rot + (i / 5) * Math.PI * 2;
                                const angle1 = rot + ((i + 1) / 5) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * size,
                                    pt.y + Math.sin(angle0) * size
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * size,
                                    pt.y + Math.sin(angle1) * size
                                );
                            }
                        } else if (v === 13) {
                            // Orbiting Planets
                            const orbitR = glowRadius + 15;
                            for (let i = 0; i < 2; i++) {
                                const angle = (i / 2) * Math.PI * 2 + t * 0.9;
                                const dx = pt.x + Math.cos(angle) * orbitR;
                                const dy = pt.y + Math.sin(angle) * orbitR;
                                g.beginFill(0xffe066, 0.9 * alphaFade);
                                g.drawCircle(dx, dy, 4 + Math.sin(t * 2 + angle));
                                g.endFill();
                            }
                        } else if (v === 14) {
                            // Cosmic Spiral
                            const segments = 36;
                            const maxRadius = glowRadius + 12;
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 8 + t * 2;
                                const radius = (i / segments) * maxRadius;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 8 + t * 2;
                                const nextRadius = ((i + 1) / segments) * maxRadius;
                                const alpha = 0.7 * alphaFade * (1 - i / segments);
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                            }
                        } else if (v === 15) {
                            // Energy Wave
                            const waveCount = 3;
                            for (let i = 0; i < waveCount; i++) {
                                const phase =
                                    (t * 1.5 + (i * Math.PI * 2) / waveCount) % (Math.PI * 2);
                                const radius = glowRadius + 8 + Math.sin(phase) * 8;
                                g.lineStyle(2, 0xffffff, 0.6 * alphaFade * (1 - i / waveCount));
                                g.drawCircle(pt.x, pt.y, radius);
                            }
                        } else if (v === 16) {
                            // Quantum Particles
                            const particleCount = 12;
                            for (let i = 0; i < particleCount; i++) {
                                const angle = (i / particleCount) * Math.PI * 2 + t;
                                const radius = glowRadius + 10 + Math.sin(t * 3 + i) * 4;
                                const dx = pt.x + Math.cos(angle) * radius;
                                const dy = pt.y + Math.sin(angle) * radius;
                                const alpha = 0.8 * alphaFade * (0.5 + 0.5 * Math.sin(t * 4 + i));
                                g.beginFill(0xffffff, alpha);
                                g.drawCircle(dx, dy, 2 + Math.sin(t * 2 + i));
                                g.endFill();
                            }
                        } else if (v === 17) {
                            // Celestial Clock
                            const hourCount = 12;
                            const radius = glowRadius + 10;
                            // Draw hour markers
                            for (let i = 0; i < hourCount; i++) {
                                const angle = (i / hourCount) * Math.PI * 2;
                                const markerLength = i % 3 === 0 ? 4 : 2;
                                g.lineStyle(2, 0xffffff, 0.8 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (radius - markerLength),
                                    pt.y + Math.sin(angle) * (radius - markerLength)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                            }
                            // Draw rotating hands
                            const hourAngle = t * 0.5;
                            const minuteAngle = t * 2;
                            g.lineStyle(2, 0xffe066, 0.9 * alphaFade);
                            g.moveTo(pt.x, pt.y);
                            g.lineTo(
                                pt.x + Math.cos(hourAngle) * radius * 0.6,
                                pt.y + Math.sin(hourAngle) * radius * 0.6
                            );
                            g.lineStyle(1.5, 0xffffff, 0.9 * alphaFade);
                            g.moveTo(pt.x, pt.y);
                            g.lineTo(
                                pt.x + Math.cos(minuteAngle) * radius * 0.8,
                                pt.y + Math.sin(minuteAngle) * radius * 0.8
                            );
                        } else if (v === 18) {
                            // Nebula Burst
                            const segments = 24;
                            const baseRadius = glowRadius + 8;
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 2;
                                const radius = baseRadius + Math.sin(t * 2 + angle * 2) * 6;
                                const nextRadius = baseRadius + Math.sin(t * 2 + nextAngle * 2) * 6;
                                g.lineStyle(2, 0xffffff, 0.6 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                            }
                        } else if (v === 19) {
                            // Stellar Wind
                            const particleCount = 16;
                            for (let i = 0; i < particleCount; i++) {
                                const angle = (i / particleCount) * Math.PI * 2;
                                const length = glowRadius + 12 + Math.sin(t * 2 + i) * 4;
                                const alpha = 0.7 * alphaFade * (0.5 + 0.5 * Math.sin(t * 3 + i));
                                g.lineStyle(1.5, 0xffffff, alpha);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * length,
                                    pt.y + Math.sin(angle) * length
                                );
                            }
                        } else if (v === 20) {
                            // Cosmic Portal
                            const segments = 32;
                            const innerRadius = glowRadius + 4;
                            const outerRadius = glowRadius + 12;
                            // Draw swirling portal
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2 + t * 2;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 2 + t * 2;
                                const alpha = 0.7 * alphaFade * (0.5 + 0.5 * Math.sin(t + i));
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * innerRadius,
                                    pt.y + Math.sin(angle) * innerRadius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * outerRadius,
                                    pt.y + Math.sin(nextAngle) * outerRadius
                                );
                            }
                            // Draw center glow
                            g.beginFill(0xffe066, 0.3 * alphaFade);
                            g.drawCircle(pt.x, pt.y, innerRadius);
                            g.endFill();
                        } else if (v === 21) {
                            // Void Rift
                            const segments = 24;
                            const baseRadius = glowRadius + 8;
                            // Draw crackling energy lines
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2 + t;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 2 + t;
                                const radius = baseRadius + Math.sin(t * 3 + i * 2) * 6;
                                const nextRadius = baseRadius + Math.sin(t * 3 + (i + 1) * 2) * 6;
                                // Main crackle
                                g.lineStyle(2, 0x000000, 0.8 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                                // Energy glow
                                g.lineStyle(1, 0x9900ff, 0.6 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (radius - 2),
                                    pt.y + Math.sin(angle) * (radius - 2)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * (nextRadius - 2),
                                    pt.y + Math.sin(nextAngle) * (nextRadius - 2)
                                );
                            }
                        } else if (v === 22) {
                            // Stellar Symphony
                            const noteCount = 8;
                            const radius = glowRadius + 10;
                            // Draw musical notes
                            for (let i = 0; i < noteCount; i++) {
                                const angle = (i / noteCount) * Math.PI * 2 + t * 0.8;
                                const dx = pt.x + Math.cos(angle) * radius;
                                const dy = pt.y + Math.sin(angle) * radius;
                                const size = 3 + Math.sin(t * 2 + i) * 1.5;
                                // Note head
                                g.beginFill(0xffffff, 0.8 * alphaFade);
                                g.drawCircle(dx, dy, size);
                                g.endFill();
                                // Note stem
                                g.lineStyle(1.5, 0xffffff, 0.8 * alphaFade);
                                g.moveTo(dx, dy);
                                g.lineTo(
                                    dx + Math.cos(angle + Math.PI / 4) * size * 2,
                                    dy + Math.sin(angle + Math.PI / 4) * size * 2
                                );
                            }
                        } else if (v === 23) {
                            // Aurora Borealis
                            const ribbonCount = 4;
                            const segments = 12;
                            const baseRadius = glowRadius + 6;
                            // Draw flowing ribbons
                            for (let i = 0; i < ribbonCount; i++) {
                                const baseAngle = (i / ribbonCount) * Math.PI * 2 + t;
                                const colors = [0x00ffff, 0x00ff00, 0xff00ff, 0xffff00];
                                g.lineStyle(3, colors[i], 0.7 * alphaFade);
                                for (let j = 0; j < segments; j++) {
                                    const angle = baseAngle + (j / segments) * Math.PI;
                                    const radius = baseRadius + Math.sin(t * 2 + j) * 8;
                                    const nextAngle = baseAngle + ((j + 1) / segments) * Math.PI;
                                    const nextRadius = baseRadius + Math.sin(t * 2 + j + 1) * 8;
                                    g.moveTo(
                                        pt.x + Math.cos(angle) * radius,
                                        pt.y + Math.sin(angle) * radius
                                    );
                                    g.lineTo(
                                        pt.x + Math.cos(nextAngle) * nextRadius,
                                        pt.y + Math.sin(nextAngle) * nextRadius
                                    );
                                }
                            }
                        } else if (v === 24) {
                            // Black Hole
                            const segments = 40;
                            const innerRadius = glowRadius + 2;
                            const outerRadius = glowRadius + 14;
                            // Draw accretion disk
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2 + t * 3;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 2 + t * 3;
                                const radius =
                                    innerRadius + (i / segments) * (outerRadius - innerRadius);
                                const nextRadius =
                                    innerRadius +
                                    ((i + 1) / segments) * (outerRadius - innerRadius);
                                const alpha = 0.7 * alphaFade * (1 - i / segments);
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                            }
                            // Draw event horizon
                            g.beginFill(0x000000, 0.9 * alphaFade);
                            g.drawCircle(pt.x, pt.y, innerRadius);
                            g.endFill();
                            // Draw particles being pulled in
                            const particleCount = 8;
                            for (let i = 0; i < particleCount; i++) {
                                const angle = (i / particleCount) * Math.PI * 2 + t * 2;
                                const radius = innerRadius + 4 + Math.sin(t * 4 + i) * 2;
                                g.beginFill(0xffffff, 0.8 * alphaFade);
                                g.drawCircle(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius,
                                    1.5
                                );
                                g.endFill();
                            }
                        } else if (v === 25) {
                            // Cosmic Prism
                            const sides = 6;
                            const size = glowRadius + 8;
                            const rot = t * 0.5;
                            // Draw crystal structure
                            for (let i = 0; i < sides; i++) {
                                const angle0 = rot + (i / sides) * Math.PI * 2;
                                const angle1 = rot + ((i + 1) / sides) * Math.PI * 2;
                                const angle2 = rot + ((i + 2) / sides) * Math.PI * 2;
                                // Main crystal face
                                g.lineStyle(2, 0xffffff, 0.8 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * size,
                                    pt.y + Math.sin(angle0) * size
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * size,
                                    pt.y + Math.sin(angle1) * size
                                );
                                // Refracted light
                                const colors = [
                                    0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff,
                                ];
                                g.lineStyle(1.5, colors[i], 0.6 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle2) * size * 0.7,
                                    pt.y + Math.sin(angle2) * size * 0.7
                                );
                            }
                        } else if (v === 26) {
                            // Nova Burst
                            const burstCount = 8;
                            const baseRadius = glowRadius + 4;
                            for (let i = 0; i < burstCount; i++) {
                                const angle = (i / burstCount) * Math.PI * 2;
                                const burstPhase = (t * 2 + i) % 1;
                                const radius = baseRadius + burstPhase * 12;
                                const alpha = 0.8 * alphaFade * (1 - burstPhase);
                                // Burst line
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                // Burst glow
                                g.beginFill(0xffe066, alpha * 0.3);
                                g.drawCircle(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius,
                                    3
                                );
                                g.endFill();
                            }
                        } else if (v === 27) {
                            // Galactic Whirlpool
                            const segments = 48;
                            const innerRadius = glowRadius + 2;
                            const outerRadius = glowRadius + 16;
                            // Draw spiral arms
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 8 + t * 2;
                                const radius =
                                    innerRadius + (i / segments) * (outerRadius - innerRadius);
                                const nextAngle = ((i + 1) / segments) * Math.PI * 8 + t * 2;
                                const nextRadius =
                                    innerRadius +
                                    ((i + 1) / segments) * (outerRadius - innerRadius);
                                const alpha = 0.7 * alphaFade * (1 - i / segments);
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                            }
                            // Draw stars in the spiral
                            const starCount = 20;
                            for (let i = 0; i < starCount; i++) {
                                const angle = (i / starCount) * Math.PI * 8 + t * 2;
                                const radius =
                                    innerRadius + (i / starCount) * (outerRadius - innerRadius);
                                const dx = pt.x + Math.cos(angle) * radius;
                                const dy = pt.y + Math.sin(angle) * radius;
                                g.beginFill(0xffffff, 0.8 * alphaFade);
                                g.drawCircle(dx, dy, 1.5 + Math.sin(t * 3 + i) * 0.5);
                                g.endFill();
                            }
                        } else if (v === 28) {
                            // Quantum Entanglement
                            const pairCount = 4;
                            const radius = glowRadius + 10;
                            for (let i = 0; i < pairCount; i++) {
                                const baseAngle = (i / pairCount) * Math.PI * 2 + t;
                                // First particle
                                const angle1 = baseAngle + Math.sin(t * 2) * 0.5;
                                const dx1 = pt.x + Math.cos(angle1) * radius;
                                const dy1 = pt.y + Math.sin(angle1) * radius;
                                // Second particle (entangled)
                                const angle2 = baseAngle + Math.PI + Math.sin(t * 2) * 0.5;
                                const dx2 = pt.x + Math.cos(angle2) * radius;
                                const dy2 = pt.y + Math.sin(angle2) * radius;
                                // Draw particles
                                g.beginFill(0x00ffff, 0.8 * alphaFade);
                                g.drawCircle(dx1, dy1, 2.5);
                                g.drawCircle(dx2, dy2, 2.5);
                                g.endFill();
                                // Draw connection
                                g.lineStyle(1, 0x00ffff, 0.4 * alphaFade);
                                g.moveTo(dx1, dy1);
                                g.lineTo(dx2, dy2);
                            }
                        } else if (v === 29) {
                            // Stellar Nebula
                            const segments = 36;
                            const baseRadius = glowRadius + 6;
                            // Draw nebula cloud
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2;
                                const nextAngle = ((i + 1) / segments) * Math.PI * 2;
                                const radius = baseRadius + Math.sin(t * 0.5 + angle * 2) * 8;
                                const nextRadius =
                                    baseRadius + Math.sin(t * 0.5 + nextAngle * 2) * 8;
                                // Create gradient effect
                                const colors = [0xff00ff, 0x00ffff, 0x00ff00, 0xff0000];
                                const colorIndex = Math.floor((i / segments) * colors.length);
                                const nextColorIndex = Math.floor(
                                    ((i + 1) / segments) * colors.length
                                );
                                g.lineStyle(3, colors[colorIndex], 0.6 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * nextRadius,
                                    pt.y + Math.sin(nextAngle) * nextRadius
                                );
                            }
                            // Add some bright spots
                            const spotCount = 6;
                            for (let i = 0; i < spotCount; i++) {
                                const angle = (i / spotCount) * Math.PI * 2 + t;
                                const radius = baseRadius + Math.sin(t * 2 + i) * 4;
                                const dx = pt.x + Math.cos(angle) * radius;
                                const dy = pt.y + Math.sin(angle) * radius;
                                g.beginFill(0xffffff, 0.9 * alphaFade);
                                g.drawCircle(dx, dy, 2 + Math.sin(t * 3 + i) * 1);
                                g.endFill();
                            }
                        } else if (v === 30) {
                            // Solar Flare
                            const flareCount = 5;
                            for (let i = 0; i < flareCount; i++) {
                                const angle = (i / flareCount) * Math.PI * 2 + t * 0.7;
                                const len = glowRadius + 14 + Math.sin(t * 2 + i) * 6;
                                const flareColor = 0xffe066;
                                g.lineStyle(3, flareColor, 0.7 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                                // Flicker highlight
                                g.lineStyle(1.5, 0xffffff, 0.5 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (len - 4),
                                    pt.y + Math.sin(angle) * (len - 4)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 31) {
                            // Event Horizon
                            const segments = 32;
                            const baseRadius = glowRadius + 10;
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2;
                                const radius = baseRadius + Math.sin(t * 3 + i) * 2;
                                const color = 0x99ccff;
                                g.lineStyle(
                                    2,
                                    color,
                                    0.5 * alphaFade + 0.2 * Math.abs(Math.sin(t + i))
                                );
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (radius - 1),
                                    pt.y + Math.sin(angle) * (radius - 1)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * (radius + 1),
                                    pt.y + Math.sin(angle) * (radius + 1)
                                );
                            }
                        } else if (v === 32) {
                            // Meteor Shower
                            const meteorCount = 6;
                            for (let i = 0; i < meteorCount; i++) {
                                const phase = (t * 1.5 + i * 0.7) % 1;
                                if (phase < 0.7) continue; // Only show when phase is near 1
                                const angle = Math.random() * Math.PI * 2;
                                const len = glowRadius + 18 + Math.random() * 8;
                                const alpha = 0.7 * alphaFade * (1 - (phase - 0.7) / 0.3);
                                g.lineStyle(2, 0xffffff, alpha);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 33) {
                            // Fractal Bloom
                            function drawFractal(x, y, radius, depth) {
                                if (depth <= 0) return;
                                const branches = 5;
                                for (let i = 0; i < branches; i++) {
                                    const angle = (i / branches) * Math.PI * 2 + t * 0.5 * depth;
                                    const nx = x + Math.cos(angle) * radius;
                                    const ny = y + Math.sin(angle) * radius;
                                    g.lineStyle(1.5, 0xffffff, (0.5 * alphaFade * depth) / 3);
                                    g.moveTo(x, y);
                                    g.lineTo(nx, ny);
                                    drawFractal(nx, ny, radius * 0.5, depth - 1);
                                }
                            }
                            drawFractal(pt.x, pt.y, 8 + Math.sin(t) * 2, 3);
                        } else if (v === 34) {
                            // Lensing Halo
                            const haloRadius = glowRadius + 10 + Math.sin(t) * 2;
                            g.lineStyle(3, 0xffffff, 0.4 * alphaFade);
                            g.drawCircle(pt.x, pt.y, haloRadius);
                            // Rainbow edge
                            for (let i = 0; i < 6; i++) {
                                const angle0 = (i / 6) * Math.PI * 2;
                                const angle1 = ((i + 1) / 6) * Math.PI * 2;
                                const color = [
                                    0xff0000, 0xffa500, 0xffff00, 0x00ff00, 0x0000ff, 0x8b00ff,
                                ][i];
                                g.lineStyle(1.5, color, 0.5 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle0) * haloRadius,
                                    pt.y + Math.sin(angle0) * haloRadius
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle1) * haloRadius,
                                    pt.y + Math.sin(angle1) * haloRadius
                                );
                            }
                        } else if (v === 35) {
                            // Solar Eclipse
                            const eclipseRadius = glowRadius + 7;
                            const shadowAngle = t * 0.7;
                            // Draw corona
                            g.lineStyle(2.5, 0xffe066, 0.7 * alphaFade);
                            g.drawCircle(pt.x, pt.y, eclipseRadius + 4 + Math.sin(t) * 2);
                            // Draw shadow disk
                            g.beginFill(0x000000, 0.8 * alphaFade);
                            g.drawCircle(
                                pt.x + Math.cos(shadowAngle) * 2,
                                pt.y + Math.sin(shadowAngle) * 2,
                                eclipseRadius
                            );
                            g.endFill();
                        } else if (v === 36) {
                            // Binary Star
                            const orbitR = glowRadius + 10;
                            for (let i = 0; i < 2; i++) {
                                const angle = t * 1.2 + i * Math.PI;
                                const dx = pt.x + Math.cos(angle) * orbitR;
                                const dy = pt.y + Math.sin(angle) * orbitR;
                                g.beginFill(0xffe066, 0.8 * alphaFade);
                                g.drawCircle(dx, dy, 3 + Math.sin(t * 2 + i));
                                g.endFill();
                            }
                        } else if (v === 37) {
                            // Comet Tail
                            const tailLength = 18;
                            const tailAngle = t * 0.8;
                            for (let i = 0; i < tailLength; i++) {
                                const frac = i / tailLength;
                                const angle = tailAngle - frac * 0.3;
                                const radius = glowRadius + 2 + frac * 18;
                                const alpha = 0.5 * alphaFade * (1 - frac);
                                g.lineStyle(2, 0x99ccff, alpha);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (radius - 2),
                                    pt.y + Math.sin(angle) * (radius - 2)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                            }
                        } else if (v === 38) {
                            // Pulsar
                            const beamAngle = t * 2;
                            const beamLen = glowRadius + 18;
                            for (let i = 0; i < 2; i++) {
                                const angle = beamAngle + i * Math.PI;
                                g.lineStyle(3, 0xffffff, 0.8 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * beamLen,
                                    pt.y + Math.sin(angle) * beamLen
                                );
                            }
                            // Pulse highlight
                            if (Math.floor(t * 2) % 2 === 0) {
                                g.beginFill(0xffe066, 0.5 * alphaFade);
                                g.drawCircle(pt.x, pt.y, 4 + Math.abs(Math.sin(t * 4)) * 2);
                                g.endFill();
                            }
                        } else if (v === 39) {
                            // Crystal Shards
                            const shardCount = 7;
                            for (let i = 0; i < shardCount; i++) {
                                const angle = (i / shardCount) * Math.PI * 2 + t * 0.5;
                                const len = glowRadius + 14 + Math.sin(t * 2 + i) * 5;
                                g.lineStyle(2.5, 0x99ccff, 0.7 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                                // Shard highlight
                                g.lineStyle(1, 0xffffff, 0.5 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (len - 3),
                                    pt.y + Math.sin(angle) * (len - 3)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 40) {
                            // Supernova Remnant
                            const ringCount = 3;
                            for (let i = 0; i < ringCount; i++) {
                                const phase = (t * 0.7 + i * 0.5) % 1;
                                const radius = glowRadius + 8 + phase * 18;
                                const alpha = 0.5 * alphaFade * (1 - phase);
                                g.lineStyle(3, 0xffe066, alpha);
                                g.drawCircle(pt.x, pt.y, radius);
                            }
                        } else if (v === 41) {
                            // Wormhole
                            const segments = 32;
                            for (let i = 0; i < segments; i++) {
                                const angle = (i / segments) * Math.PI * 2 + t * 2;
                                const radius = glowRadius + 6 + Math.sin(t * 3 + i) * 6;
                                const color = 0x00ffff;
                                g.lineStyle(2, color, 0.5 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * radius,
                                    pt.y + Math.sin(angle) * radius
                                );
                            }
                            // Spiral overlay
                            for (let i = 0; i < 2; i++) {
                                const spiralR = glowRadius + 10;
                                for (let j = 0; j < segments; j++) {
                                    const angle =
                                        (j / segments) * Math.PI * 2 + t * (i === 0 ? 1 : -1);
                                    const nextAngle =
                                        ((j + 1) / segments) * Math.PI * 2 + t * (i === 0 ? 1 : -1);
                                    g.lineStyle(1.5, 0x8c6cc0, 0.4 * alphaFade);
                                    g.moveTo(
                                        pt.x + Math.cos(angle) * spiralR,
                                        pt.y + Math.sin(angle) * spiralR
                                    );
                                    g.lineTo(
                                        pt.x + Math.cos(nextAngle) * spiralR,
                                        pt.y + Math.sin(nextAngle) * spiralR
                                    );
                                }
                            }
                        } else if (v === 42) {
                            // Solar Prominence
                            const arcCount = 4;
                            for (let i = 0; i < arcCount; i++) {
                                const angle = (i / arcCount) * Math.PI * 2 + t * 0.8;
                                const startR = glowRadius + 7;
                                const endR = glowRadius + 16 + Math.sin(t * 2 + i) * 3;
                                const midAngle = angle + 0.3;
                                g.lineStyle(2.5, 0xffe066, 0.7 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * startR,
                                    pt.y + Math.sin(angle) * startR
                                );
                                g.quadraticCurveTo(
                                    pt.x + (Math.cos(midAngle) * (startR + endR)) / 2,
                                    pt.y + (Math.sin(midAngle) * (startR + endR)) / 2 - 6,
                                    pt.x + Math.cos(angle) * endR,
                                    pt.y + Math.sin(angle) * endR
                                );
                            }
                        } else if (v === 43) {
                            // Dark Matter Veil
                            const tendrilCount = 8;
                            for (let i = 0; i < tendrilCount; i++) {
                                const angle = (i / tendrilCount) * Math.PI * 2 + t * 0.3;
                                const len = glowRadius + 10 + Math.sin(t * 2 + i) * 8;
                                g.lineStyle(2, 0x222222, 0.5 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                                // Wispy highlight
                                g.lineStyle(1, 0x8c6cc0, 0.2 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (len - 4),
                                    pt.y + Math.sin(angle) * (len - 4)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 44) {
                            // Gamma Burst
                            const burstCount = 5;
                            for (let i = 0; i < burstCount; i++) {
                                const angle = (i / burstCount) * Math.PI * 2 + t * 2.5;
                                const len = glowRadius + 20 + Math.abs(Math.sin(t * 3 + i)) * 8;
                                g.lineStyle(3, 0xffffff, 0.8 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 45) {
                            // Orbiting Moons
                            const moonCount = 3;
                            const orbitR = glowRadius + 13;
                            for (let i = 0; i < moonCount; i++) {
                                const angle = t * (1.1 + i * 0.2) + (i / moonCount) * Math.PI * 2;
                                const dx = pt.x + Math.cos(angle) * orbitR;
                                const dy = pt.y + Math.sin(angle) * orbitR;
                                g.beginFill(0xffffff, 0.7 * alphaFade);
                                g.drawCircle(dx, dy, 2.5 + Math.sin(t * 2 + i));
                                g.endFill();
                            }
                        } else if (v === 46) {
                            // Plasma Stream
                            const streamCount = 4;
                            for (let i = 0; i < streamCount; i++) {
                                const angle = (i / streamCount) * Math.PI * 2 + t * 0.7;
                                const len = glowRadius + 16 + Math.sin(t * 2 + i) * 4;
                                g.lineStyle(2, 0x00ffff, 0.6 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                                // Animated arc
                                g.lineStyle(1, 0xffffff, 0.3 * alphaFade);
                                g.moveTo(
                                    pt.x + Math.cos(angle) * (len - 5),
                                    pt.y + Math.sin(angle) * (len - 5)
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 47) {
                            // Frozen Halo
                            const haloRadius = glowRadius + 12 + Math.sin(t) * 2;
                            g.lineStyle(3, 0x99ccff, 0.5 * alphaFade);
                            g.drawCircle(pt.x, pt.y, haloRadius);
                            // Sparkling points
                            const sparkCount = 8;
                            for (let i = 0; i < sparkCount; i++) {
                                const angle = (i / sparkCount) * Math.PI * 2 + t * 1.5;
                                const dx = pt.x + Math.cos(angle) * haloRadius;
                                const dy = pt.y + Math.sin(angle) * haloRadius;
                                g.beginFill(0xffffff, 0.8 * alphaFade);
                                g.drawCircle(dx, dy, 1.5 + Math.abs(Math.sin(t * 3 + i)));
                                g.endFill();
                            }
                        } else if (v === 48) {
                            // Magnetar Field
                            const fieldCount = 6;
                            for (let i = 0; i < fieldCount; i++) {
                                const angle = (i / fieldCount) * Math.PI * 2 + t * 0.5;
                                const len = glowRadius + 14 + Math.sin(t * 2 + i) * 6;
                                g.lineStyle(2, 0x00ff99, 0.6 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.quadraticCurveTo(
                                    pt.x + Math.cos(angle + 0.5) * (len - 6),
                                    pt.y + Math.sin(angle + 0.5) * (len - 6),
                                    pt.x + Math.cos(angle) * len,
                                    pt.y + Math.sin(angle) * len
                                );
                            }
                        } else if (v === 49) {
                            // Celestial Butterfly
                            const wingSpan = glowRadius + 14 + Math.sin(t) * 3;
                            for (let i = 0; i < 2; i++) {
                                const angle = (i === 0 ? -0.7 : 0.7) + Math.sin(t * 2) * 0.1;
                                g.lineStyle(3, 0xffb3ff, 0.7 * alphaFade);
                                g.moveTo(pt.x, pt.y);
                                g.bezierCurveTo(
                                    pt.x + Math.cos(angle) * (wingSpan * 0.7),
                                    pt.y + Math.sin(angle) * (wingSpan * 0.7) - 8,
                                    pt.x + Math.cos(angle) * (wingSpan * 1.1),
                                    pt.y + Math.sin(angle) * (wingSpan * 1.1) + 8,
                                    pt.x + Math.cos(angle) * wingSpan,
                                    pt.y + Math.sin(angle) * wingSpan
                                );
                            }
                            // Body
                            g.lineStyle(2, 0xffffff, 0.8 * alphaFade);
                            g.moveTo(pt.x, pt.y - 4);
                            g.lineTo(pt.x, pt.y + 4);
                        } else if (v === 50) {
                            // Procedural Sigil (rare, unique per point)
                            if (!pt._sigilDef) {
                                // Generate a random sigil definition and store it on the point
                                const arms = 3 + Math.floor(Math.random() * 4); // 3-6 arms
                                const dots = 2 + Math.floor(Math.random() * 3); // 2-4 dots
                                const colorPal = [0x00ffff, 0xff66cc, 0xffe066];
                                const color = colorPal[Math.floor(Math.random() * colorPal.length)];
                                const dotColor =
                                    colorPal[
                                        (Math.floor(Math.random() * colorPal.length) + 1) %
                                            colorPal.length
                                    ];
                                const armDefs = [];
                                for (let i = 0; i < arms; i++) {
                                    const angle = (i / arms) * Math.PI * 2 + Math.random() * 0.2;
                                    const len = 8 + Math.random() * 8;
                                    const curve = Math.random() * 0.7 - 0.35; // -0.35 to 0.35
                                    armDefs.push({ angle, len, curve });
                                }
                                const dotDefs = [];
                                for (let i = 0; i < dots; i++) {
                                    const angle = Math.random() * Math.PI * 2;
                                    const radius = 5 + Math.random() * 10;
                                    dotDefs.push({ angle, radius });
                                }
                                pt._sigilDef = { arms, armDefs, dots, dotDefs, color, dotColor };
                            }
                            const sigil = pt._sigilDef;
                            // Animate rotation and pulse
                            const sigilRot = t * 0.7 + pt.phase;
                            const sigilPulse = 1.0 + 0.18 * Math.sin(t * 2.2 + pt.phase);
                            // Draw glowing aura
                            g.beginFill(sigil.color, 0.18 * alphaFade);
                            g.drawCircle(pt.x, pt.y, (glowRadius + 7) * sigilPulse);
                            g.endFill();
                            // Draw arms/branches
                            for (const arm of sigil.armDefs) {
                                const angle = arm.angle + sigilRot;
                                const len = arm.len * sigilPulse;
                                const cx = pt.x + Math.cos(angle) * (coreRadius + 2);
                                const cy = pt.y + Math.sin(angle) * (coreRadius + 2);
                                const ex = pt.x + Math.cos(angle) * (coreRadius + len);
                                const ey = pt.y + Math.sin(angle) * (coreRadius + len);
                                // Optionally, add a curve
                                if (Math.abs(arm.curve) > 0.1) {
                                    const mx =
                                        pt.x +
                                        Math.cos(angle + arm.curve) * (coreRadius + len * 0.6);
                                    const my =
                                        pt.y +
                                        Math.sin(angle + arm.curve) * (coreRadius + len * 0.6);
                                    g.lineStyle(2.2, sigil.color, 0.85 * alphaFade);
                                    g.moveTo(cx, cy);
                                    g.quadraticCurveTo(mx, my, ex, ey);
                                } else {
                                    g.lineStyle(2.2, sigil.color, 0.85 * alphaFade);
                                    g.moveTo(cx, cy);
                                    g.lineTo(ex, ey);
                                }
                                // Add a glowing tip
                                g.beginFill(sigil.color, 0.7 * alphaFade);
                                g.drawCircle(ex, ey, 1.7 + Math.abs(Math.sin(t * 2 + arm.angle)));
                                g.endFill();
                            }
                            // Draw dots
                            for (const dot of sigil.dotDefs) {
                                const angle = dot.angle + sigilRot * 0.7;
                                const radius = dot.radius * sigilPulse;
                                const dx = pt.x + Math.cos(angle) * radius;
                                const dy = pt.y + Math.sin(angle) * radius;
                                g.beginFill(sigil.dotColor, 0.8 * alphaFade);
                                g.drawCircle(dx, dy, 2.2 + Math.abs(Math.sin(t * 2 + dot.angle)));
                                g.endFill();
                            }
                            // Draw a central core
                            g.beginFill(sigil.color, 0.95 * alphaFade);
                            g.drawCircle(pt.x, pt.y, 2.5 + Math.abs(Math.sin(t * 2 + pt.phase)));
                            g.endFill();
                        } else if (v === 64) {
                            // Existing last reticle (leave as is)
                            // ... existing code ...
                        } else if (v === 65) {
                            // Flower of Life inspired pattern
                            const circles = 7;
                            const baseR = glowRadius + 8;
                            const rot = t * 0.3 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < circles; i++) {
                                const angle = rot + (i / circles) * Math.PI * 2;
                                const x = pt.x + Math.cos(angle) * baseR;
                                const y = pt.y + Math.sin(angle) * baseR;
                                g.drawCircle(x, y, baseR);
                            }
                            g.drawCircle(pt.x, pt.y, baseR);
                        } else if (v === 66) {
                            // Metatron's Cube inspired pattern
                            const points = 6;
                            const r1 = glowRadius + 10;
                            const r2 = glowRadius + 18;
                            const rot = t * 0.4 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            // Draw outer hexagon
                            for (let i = 0; i < points; i++) {
                                const angle1 = rot + (i / points) * Math.PI * 2;
                                const angle2 = rot + ((i + 1) / points) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle1) * r2,
                                    pt.y + Math.sin(angle1) * r2
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle2) * r2,
                                    pt.y + Math.sin(angle2) * r2
                                );
                            }
                            // Draw inner connections
                            for (let i = 0; i < points; i++) {
                                const angle = rot + (i / points) * Math.PI * 2;
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(pt.x + Math.cos(angle) * r2, pt.y + Math.sin(angle) * r2);
                            }
                            g.drawCircle(pt.x, pt.y, r1);
                        } else if (v === 67) {
                            // Sri Yantra inspired pattern
                            const triangles = 9;
                            const r = glowRadius + 15;
                            const rot = t * 0.5 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < triangles; i++) {
                                const angle = rot + (i / triangles) * Math.PI * 2;
                                for (let j = 0; j < 3; j++) {
                                    const a1 = angle + (j / 3) * Math.PI * 2;
                                    const a2 = angle + ((j + 1) / 3) * Math.PI * 2;
                                    g.moveTo(pt.x + Math.cos(a1) * r, pt.y + Math.sin(a1) * r);
                                    g.lineTo(pt.x + Math.cos(a2) * r, pt.y + Math.sin(a2) * r);
                                }
                            }
                            g.drawCircle(pt.x, pt.y, r * 0.5);
                        } else if (v === 68) {
                            // Seed of Life inspired pattern
                            const circles = 6;
                            const r = glowRadius + 12;
                            const rot = t * 0.3 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < circles; i++) {
                                const angle = rot + (i / circles) * Math.PI * 2;
                                const x = pt.x + Math.cos(angle) * r;
                                const y = pt.y + Math.sin(angle) * r;
                                g.drawCircle(x, y, r);
                            }
                            g.drawCircle(pt.x, pt.y, r);
                        } else if (v === 69) {
                            // Tree of Life inspired pattern
                            const points = 10;
                            const r = glowRadius + 15;
                            const rot = t * 0.4 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            // Draw sephiroth
                            for (let i = 0; i < points; i++) {
                                const angle = rot + (i / points) * Math.PI * 2;
                                const x = pt.x + Math.cos(angle) * r;
                                const y = pt.y + Math.sin(angle) * r;
                                g.drawCircle(x, y, r * 0.2);
                            }
                            // Draw connections
                            for (let i = 0; i < points; i++) {
                                const angle = rot + (i / points) * Math.PI * 2;
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(pt.x + Math.cos(angle) * r, pt.y + Math.sin(angle) * r);
                            }
                        } else if (v === 70) {
                            // Vesica Piscis inspired pattern
                            const circles = 3;
                            const r = glowRadius + 12;
                            const rot = t * 0.3 + pt.phase;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < circles; i++) {
                                const angle = rot + (i / circles) * Math.PI * 2;
                                const x = pt.x + Math.cos(angle) * r;
                                const y = pt.y + Math.sin(angle) * r;
                                g.drawCircle(x, y, r);
                            }
                            g.drawCircle(pt.x, pt.y, r);
                        } else if (v === 71) {
                            // 4D Hypercube (Tesseract) projection
                            const size = glowRadius + 10;
                            const rot = t * 0.5 + pt.phase;
                            const vertices = [];
                            for (let i = 0; i < 16; i++) {
                                let x = (i & 1 ? 1 : -1) * size * 0.7;
                                let y = (i & 2 ? 1 : -1) * size * 0.7;
                                let z = (i & 4 ? 1 : -1) * size * 0.7;
                                let w = (i & 8 ? 1 : -1) * size * 0.7;
                                // 4D rotation
                                let zw = z * Math.cos(rot) - w * Math.sin(rot);
                                let wz = z * Math.sin(rot) + w * Math.cos(rot);
                                let xw = x * Math.cos(rot * 0.7) - w * Math.sin(rot * 0.7);
                                let wx = x * Math.sin(rot * 0.7) + w * Math.cos(rot * 0.7);
                                // Project to 2D
                                let px = pt.x + (xw + zw) * 0.5;
                                let py = pt.y + (y + wz) * 0.5;
                                vertices.push([px, py]);
                            }
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < 16; i++) {
                                for (let j = 0; j < 4; j++) {
                                    let k = i ^ (1 << j);
                                    if (i < k) {
                                        g.moveTo(vertices[i][0], vertices[i][1]);
                                        g.lineTo(vertices[k][0], vertices[k][1]);
                                    }
                                }
                            }
                        } else if (v === 72) {
                            // Sierpinski Triangle
                            function drawSierpinski(x, y, size, depth) {
                                if (depth <= 0) return;
                                const height = (size * Math.sqrt(3)) / 2;
                                // Draw current triangle
                                g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                                g.moveTo(x, y - height / 2);
                                g.lineTo(x - size / 2, y + height / 2);
                                g.lineTo(x + size / 2, y + height / 2);
                                g.lineTo(x, y - height / 2);
                                // Recursively draw smaller triangles
                                const newSize = size / 2;
                                const newHeight = height / 2;
                                drawSierpinski(x, y - newHeight / 2, newSize, depth - 1);
                                drawSierpinski(
                                    x - newSize / 2,
                                    y + newHeight / 2,
                                    newSize,
                                    depth - 1
                                );
                                drawSierpinski(
                                    x + newSize / 2,
                                    y + newHeight / 2,
                                    newSize,
                                    depth - 1
                                );
                            }
                            const size = glowRadius + 15;
                            drawSierpinski(pt.x, pt.y, size, 4);
                        } else if (v === 73) {
                            // 3D Dodecahedron projection
                            const size = glowRadius + 10;
                            const rot = t * 0.5 + pt.phase;
                            const phi = (1 + Math.sqrt(5)) / 2; // Golden ratio
                            const vertices = [];
                            // Generate dodecahedron vertices
                            for (let i = 0; i < 20; i++) {
                                let x, y, z;
                                if (i < 8) {
                                    x = (i & 1 ? 1 : -1) * size * 0.5;
                                    y = (i & 2 ? 1 : -1) * size * 0.5;
                                    z = (i & 4 ? 1 : -1) * size * 0.5;
                                } else {
                                    const j = i - 8;
                                    x = (j & 1 ? phi : -phi) * size * 0.3;
                                    y = (j & 2 ? phi : -phi) * size * 0.3;
                                    z = (j & 4 ? phi : -phi) * size * 0.3;
                                }
                                // 3D rotation
                                const cosX = Math.cos(rot);
                                const sinX = Math.sin(rot);
                                const cosY = Math.cos(rot * 0.7);
                                const sinY = Math.sin(rot * 0.7);
                                const tempX = x * cosX - z * sinX;
                                const tempZ = x * sinX + z * cosX;
                                const tempY = y * cosY - tempZ * sinY;
                                const finalZ = y * sinY + tempZ * cosY;
                                // Project to 2D
                                const px = pt.x + tempX;
                                const py = pt.y + tempY;
                                vertices.push([px, py]);
                            }
                            // Draw edges
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < vertices.length; i++) {
                                for (let j = i + 1; j < vertices.length; j++) {
                                    const dx = vertices[i][0] - vertices[j][0];
                                    const dy = vertices[i][1] - vertices[j][1];
                                    const dist = Math.sqrt(dx * dx + dy * dy);
                                    if (dist < size * 0.8) {
                                        g.moveTo(vertices[i][0], vertices[i][1]);
                                        g.lineTo(vertices[j][0], vertices[j][1]);
                                    }
                                }
                            }
                        } else if (v === 74) {
                            // Sacred Geometry Grid
                            const size = glowRadius + 15;
                            const rot = t * 0.3 + pt.phase;
                            const gridSize = 3;
                            const cellSize = size / gridSize;

                            // Draw main grid
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = -gridSize; i <= gridSize; i++) {
                                for (let j = -gridSize; j <= gridSize; j++) {
                                    const x = pt.x + i * cellSize;
                                    const y = pt.y + j * cellSize;
                                    // Draw cell
                                    g.drawCircle(x, y, cellSize * 0.4);
                                    // Draw connecting lines
                                    if (i < gridSize) {
                                        g.moveTo(x, y);
                                        g.lineTo(x + cellSize, y);
                                    }
                                    if (j < gridSize) {
                                        g.moveTo(x, y);
                                        g.lineTo(x, y + cellSize);
                                    }
                                }
                            }

                            // Draw rotating inner pattern
                            const innerSize = size * 0.6;
                            for (let i = 0; i < 8; i++) {
                                const angle = rot + (i / 8) * Math.PI * 2;
                                const nextAngle = rot + ((i + 1) / 8) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle) * innerSize,
                                    pt.y + Math.sin(angle) * innerSize
                                );
                                g.lineTo(
                                    pt.x + Math.cos(nextAngle) * innerSize,
                                    pt.y + Math.sin(nextAngle) * innerSize
                                );
                            }
                        } else if (v === 75) {
                            // 4D 24-cell projection
                            const size = glowRadius + 10;
                            const rot = t * 0.5 + pt.phase;
                            const vertices = [];

                            // Generate vertices of 24-cell
                            for (let i = 0; i < 24; i++) {
                                let x = 0,
                                    y = 0,
                                    z = 0,
                                    w = 0;
                                if (i < 8) {
                                    x = (i & 1 ? 1 : -1) * size * 0.7;
                                    y = (i & 2 ? 1 : -1) * size * 0.7;
                                    z = (i & 4 ? 1 : -1) * size * 0.7;
                                } else if (i < 16) {
                                    w = (i & 1 ? 1 : -1) * size * 0.7;
                                    x = (i & 2 ? 1 : -1) * size * 0.7;
                                    y = (i & 4 ? 1 : -1) * size * 0.7;
                                } else {
                                    w = (i & 1 ? 1 : -1) * size * 0.7;
                                    x = (i & 2 ? 1 : -1) * size * 0.7;
                                    z = (i & 4 ? 1 : -1) * size * 0.7;
                                }

                                // 4D rotation
                                let zw = z * Math.cos(rot) - w * Math.sin(rot);
                                let wz = z * Math.sin(rot) + w * Math.cos(rot);
                                let xw = x * Math.cos(rot * 0.7) - w * Math.sin(rot * 0.7);
                                let wx = x * Math.sin(rot * 0.7) + w * Math.cos(rot * 0.7);

                                // Project to 2D
                                let px = pt.x + (xw + zw) * 0.5;
                                let py = pt.y + (y + wz) * 0.5;
                                vertices.push([px, py]);
                            }

                            // Draw edges
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < vertices.length; i++) {
                                for (let j = i + 1; j < vertices.length; j++) {
                                    const dx = vertices[i][0] - vertices[j][0];
                                    const dy = vertices[i][1] - vertices[j][1];
                                    const dist = Math.sqrt(dx * dx + dy * dy);
                                    if (dist < size * 0.8) {
                                        g.moveTo(vertices[i][0], vertices[i][1]);
                                        g.lineTo(vertices[j][0], vertices[j][1]);
                                    }
                                }
                            }
                        } else if (v === 76) {
                            // Optical Illusion (Spinning Concentric Circles)
                            const circles = 5;
                            const baseRadius = glowRadius + 5;
                            const rot = t * 0.3 + pt.phase;
                            for (let i = 0; i < circles; i++) {
                                const radius = baseRadius + i * 5;
                                const color = i % 2 === 0 ? 0xffffff : 0x000000;
                                g.lineStyle(2, color, 0.7 * alphaFade);
                                g.drawCircle(pt.x, pt.y, radius);
                            }
                            // Inner rotating circle
                            const innerRadius = baseRadius * 0.5;
                            g.lineStyle(2, 0xff0000, 0.7 * alphaFade);
                            g.drawCircle(
                                pt.x + Math.cos(rot) * innerRadius,
                                pt.y + Math.sin(rot) * innerRadius,
                                innerRadius
                            );
                        } else if (v === 77) {
                            // Mandala Spiral
                            const size = glowRadius + 12;
                            const rot = t * 0.3 + pt.phase;
                            const layers = 8;
                            const pointsPerLayer = 12;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let layer = 0; layer < layers; layer++) {
                                const layerSize = size * (1 - layer / layers);
                                const layerRot = rot + (layer * Math.PI) / 4;
                                for (let i = 0; i < pointsPerLayer; i++) {
                                    const angle = layerRot + (i / pointsPerLayer) * Math.PI * 2;
                                    const x = pt.x + Math.cos(angle) * layerSize;
                                    const y = pt.y + Math.sin(angle) * layerSize;
                                    g.moveTo(pt.x, pt.y);
                                    g.lineTo(x, y);
                                    const nextAngle =
                                        layerRot + ((i + 1) / pointsPerLayer) * Math.PI * 2;
                                    const nextX = pt.x + Math.cos(nextAngle) * layerSize;
                                    const nextY = pt.y + Math.sin(nextAngle) * layerSize;
                                    g.moveTo(x, y);
                                    g.lineTo(nextX, nextY);
                                }
                            }
                        } else if (v === 78) {
                            // 4D 120-cell projection (simplified)
                            const size = glowRadius + 10;
                            const rot = t * 0.5 + pt.phase;
                            const vertices = [];
                            const phi = (1 + Math.sqrt(5)) / 2;
                            for (let i = 0; i < 14; i++) {
                                let x = Math.cos(i) * size * 0.7;
                                let y = Math.sin(i) * size * 0.7;
                                let z = Math.cos(i * phi) * size * 0.7;
                                let w = Math.sin(i * phi) * size * 0.7;
                                let zw = z * Math.cos(rot) - w * Math.sin(rot);
                                let wz = z * Math.sin(rot) + w * Math.cos(rot);
                                let xw = x * Math.cos(rot * 0.7) - w * Math.sin(rot * 0.7);
                                let wx = x * Math.sin(rot * 0.7) + w * Math.cos(rot * 0.7);
                                let px = pt.x + (xw + zw) * 0.5;
                                let py = pt.y + (y + wz) * 0.5;
                                vertices.push([px, py]);
                            }
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < vertices.length; i++) {
                                for (let j = i + 1; j < vertices.length; j++) {
                                    const dx = vertices[i][0] - vertices[j][0];
                                    const dy = vertices[i][1] - vertices[j][1];
                                    const dist = Math.sqrt(dx * dx + dy * dy);
                                    if (dist < size * 0.8) {
                                        g.moveTo(vertices[i][0], vertices[i][1]);
                                        g.lineTo(vertices[j][0], vertices[j][1]);
                                    }
                                }
                            }
                        } else if (v === 79) {
                            // Sacred Geometry Star (7-pointed)
                            const size = glowRadius + 15;
                            const rot = t * 0.3 + pt.phase;
                            const points = 7;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < points; i++) {
                                const angle1 = rot + (i / points) * Math.PI * 2;
                                const angle2 = rot + ((i + 3) / points) * Math.PI * 2;
                                g.moveTo(
                                    pt.x + Math.cos(angle1) * size,
                                    pt.y + Math.sin(angle1) * size
                                );
                                g.lineTo(
                                    pt.x + Math.cos(angle2) * size,
                                    pt.y + Math.sin(angle2) * size
                                );
                            }
                            const innerSize = size * 0.6;
                            g.drawCircle(pt.x, pt.y, innerSize);
                            g.drawCircle(pt.x, pt.y, innerSize * 0.5);
                            for (let i = 0; i < points; i++) {
                                const angle = rot + (i / points) * Math.PI * 2;
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(
                                    pt.x + Math.cos(angle) * innerSize,
                                    pt.y + Math.sin(angle) * innerSize
                                );
                            }
                        } else if (v === 80) {
                            // Penrose Tiling (kite and dart, simplified radial)
                            const size = glowRadius + 13;
                            const rot = t * 0.25 + pt.phase;
                            const kites = 10;
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            for (let i = 0; i < kites; i++) {
                                const angle = rot + (i / kites) * Math.PI * 2;
                                const nextAngle = rot + ((i + 1) / kites) * Math.PI * 2;
                                // Draw kite
                                const x0 = pt.x;
                                const y0 = pt.y;
                                const x1 = pt.x + Math.cos(angle) * size;
                                const y1 = pt.y + Math.sin(angle) * size;
                                const x2 = pt.x + Math.cos((angle + nextAngle) / 2) * (size * 0.6);
                                const y2 = pt.y + Math.sin((angle + nextAngle) / 2) * (size * 0.6);
                                const x3 = pt.x + Math.cos(nextAngle) * size;
                                const y3 = pt.y + Math.sin(nextAngle) * size;
                                g.moveTo(x0, y0);
                                g.lineTo(x1, y1);
                                g.lineTo(x2, y2);
                                g.lineTo(x3, y3);
                                g.lineTo(x0, y0);
                            }
                            // Overlay star
                            for (let i = 0; i < kites; i++) {
                                const angle = rot + (i / kites) * Math.PI * 2;
                                const x = pt.x + Math.cos(angle) * size * 0.8;
                                const y = pt.y + Math.sin(angle) * size * 0.8;
                                g.moveTo(pt.x, pt.y);
                                g.lineTo(x, y);
                            }
                        } else if (v === 81) {
                            // 4D 600-cell projection (icosahedral symmetry, simplified)
                            const size = glowRadius + 10;
                            const rot = t * 0.4 + pt.phase;
                            const phi = (1 + Math.sqrt(5)) / 2;
                            const vertices = [];
                            for (let i = 0; i < 1200; i++) {
                                let a = (2 * Math.PI * i) / 120;
                                let b = (2 * Math.PI * ((i * 7) % 120)) / 120;
                                let x = Math.cos(a) * size * 0.7;
                                let y = Math.sin(a) * size * 0.7;
                                let z = Math.cos(b) * size * 0.7;
                                let w = Math.sin(b) * size * 0.7;
                                // 4D rotation
                                let zw = z * Math.cos(rot) - w * Math.sin(rot);
                                let wz = z * Math.sin(rot) + w * Math.cos(rot);
                                let xw = x * Math.cos(rot * 0.7) - w * Math.sin(rot * 0.7);
                                let wx = x * Math.sin(rot * 0.7) + w * Math.cos(rot * 0.7);
                                let px = pt.x + (xw + zw) * 0.5;
                                let py = pt.y + (y + wz) * 0.5;
                                vertices.push([px, py]);
                            }
                            g.lineStyle(1.2, pt.sunColor, 0.6 * alphaFade);
                            for (let i = 0; i < vertices.length; i++) {
                                for (let j = i + 1; j < vertices.length; j++) {
                                    const dx = vertices[i][0] - vertices[j][0];
                                    const dy = vertices[i][1] - vertices[j][1];
                                    const dist = Math.sqrt(dx * dx + dy * dy);
                                    if (dist < size * 0.6) {
                                        g.moveTo(vertices[i][0], vertices[i][1]);
                                        g.lineTo(vertices[j][0], vertices[j][1]);
                                    }
                                }
                            }
                        } else if (v === 82) {
                            // Fractal Hexagon Web (recursive, 2 levels)
                            function drawHexWeb(x, y, r, depth) {
                                if (depth === 0) return;
                                for (let i = 0; i < 6; i++) {
                                    const angle1 = (i / 6) * Math.PI * 2;
                                    const angle2 = ((i + 1) / 6) * Math.PI * 2;
                                    const x1 = x + Math.cos(angle1) * r;
                                    const y1 = y + Math.sin(angle1) * r;
                                    const x2 = x + Math.cos(angle2) * r;
                                    const y2 = y + Math.sin(angle2) * r;
                                    g.moveTo(x, y);
                                    g.lineTo(x1, y1);
                                    g.lineTo(x2, y2);
                                    g.lineTo(x, y);
                                    // Recursive call
                                    drawHexWeb(x1, y1, r * 0.5, depth - 1);
                                }
                            }
                            g.lineStyle(1.5, pt.sunColor, 0.7 * alphaFade);
                            drawHexWeb(pt.x, pt.y, glowRadius + 10, 2);
                        } else if (v === 83) {
                            // Spirograph (Lissajous Curve)
                            const points = 80;
                            const size = glowRadius + 10;
                            const a = 5,
                                b = 7; // Try different coprime values for different patterns
                            const rot = t * 0.2 + pt.phase;
                            g.lineStyle(1.2, pt.sunColor, 0.6 * alphaFade);
                            let prev = null;
                            for (let i = 0; i <= points; i++) {
                                const t2 = (i / points) * Math.PI * 2;
                                const x = pt.x + Math.cos(a * t2 + rot) * size * 0.7;
                                const y = pt.y + Math.sin(b * t2 + rot) * size * 0.7;
                                if (prev) {
                                    g.moveTo(prev[0], prev[1]);
                                    g.lineTo(x, y);
                                }
                                prev = [x, y];
                            }
                        } else if (v === 84) {
                            // Nested Polygons
                            const layers = 3;
                            const sides = 6;
                            const size = glowRadius + 8;
                            const rot = t * 0.2 + pt.phase;
                            g.lineStyle(1.2, pt.sunColor, 0.6 * alphaFade);
                            for (let l = 1; l <= layers; l++) {
                                for (let i = 0; i < sides; i++) {
                                    const angle1 = rot + (i / sides) * Math.PI * 2;
                                    const angle2 = rot + ((i + 1) / sides) * Math.PI * 2;
                                    const r = size * (l / layers);
                                    g.moveTo(
                                        pt.x + Math.cos(angle1) * r,
                                        pt.y + Math.sin(angle1) * r
                                    );
                                    g.lineTo(
                                        pt.x + Math.cos(angle2) * r,
                                        pt.y + Math.sin(angle2) * r
                                    );
                                }
                            }
                        } else if (v === 85) {
                            // Golden Spiral (Logarithmic Spiral)
                            const turns = 3;
                            const points = 60;
                            const size = glowRadius + 10;
                            const rot = t * 0.2 + pt.phase;
                            const a = 0.3,
                                b = 0.22; // Spiral parameters
                            g.lineStyle(1.2, pt.sunColor, 0.6 * alphaFade);
                            let prev = null;
                            for (let i = 0; i <= points; i++) {
                                const theta = (i / points) * Math.PI * 2 * turns + rot;
                                const r = size * a * Math.exp(b * theta);
                                const x = pt.x + Math.cos(theta) * r;
                                const y = pt.y + Math.sin(theta) * r;
                                if (prev) {
                                    g.moveTo(prev[0], prev[1]);
                                    g.lineTo(x, y);
                                }
                                prev = [x, y];
                            }
                        }
                    }
                }

                // --- Alien Nameplate Feature ---
                if (pt._alienNameGlyph === undefined) {
                    // 1 in 3 chance to have a nameplate
                    pt._alienNameGlyph = null;
                    if (Math.random() < 0.33) {
                        // Generate a glyph: 4-8 characters
                        const charCount = 4 + Math.floor(Math.random() * 5);
                        const chars = [];
                        for (let i = 0; i < charCount; i++) {
                            const type = Math.floor(Math.random() * 4); // 0=line, 1=dot, 2=arc, 3=zigzag
                            chars.push({ type, seed: Math.random() });
                        }
                        pt._alienNameGlyph = chars;
                    }
                }
                if (pt._alienNameGlyph) {
                    // Draw the glyph horizontally below the point
                    const px = pt.x;
                    const py = pt.y + (glowRadius + 22); // below the point
                    const charSpacing = 10;
                    const glyphWidth = pt._alienNameGlyph.length * charSpacing;
                    const startX = px - glyphWidth / 2 + charSpacing / 2;
                    // Glow effect
                    g.lineStyle(4, 0xffffff, 0.18 * alphaFade);
                    for (let i = 0; i < pt._alienNameGlyph.length; i++) {
                        const x = startX + i * charSpacing;
                        const c = pt._alienNameGlyph[i];
                        switch (c.type) {
                            case 0: // line
                                g.moveTo(x - 3, py);
                                g.lineTo(x + 3, py);
                                break;
                            case 1: // dot
                                g.drawCircle(x, py, 2);
                                break;
                            case 2: // arc
                                g.arc(
                                    x,
                                    py,
                                    4,
                                    Math.PI * (0.1 + c.seed * 0.6),
                                    Math.PI * (0.9 + c.seed * 0.6)
                                );
                                break;
                            case 3: // zigzag
                                g.moveTo(x - 3, py + 2);
                                g.lineTo(x, py - 2);
                                g.lineTo(x + 3, py + 2);
                                break;
                        }
                    }
                    // Main white shapes
                    g.lineStyle(2, 0xffffff, 0.85 * alphaFade);
                    for (let i = 0; i < pt._alienNameGlyph.length; i++) {
                        const x = startX + i * charSpacing;
                        const c = pt._alienNameGlyph[i];
                        switch (c.type) {
                            case 0: // line
                                g.moveTo(x - 3, py);
                                g.lineTo(x + 3, py);
                                break;
                            case 1: // dot
                                g.drawCircle(x, py, 2);
                                break;
                            case 2: // arc
                                g.arc(
                                    x,
                                    py,
                                    4,
                                    Math.PI * (0.1 + c.seed * 0.6),
                                    Math.PI * (0.9 + c.seed * 0.6)
                                );
                                break;
                            case 3: // zigzag
                                g.moveTo(x - 3, py + 2);
                                g.lineTo(x, py - 2);
                                g.lineTo(x + 3, py + 2);
                                break;
                        }
                    }
                }

                // Starburst/cross flare (for any point)
                if (pt.hasStarburst) {
                    g.lineStyle(1.2, 0xffffff, 0.7 * alphaFade);
                    const len = (pt.isAnchor ? 10 : 7) + (pt.isLegendary ? 2 : 0);
                    g.moveTo(pt.x - len, pt.y);
                    g.lineTo(pt.x + len, pt.y);
                    g.moveTo(pt.x, pt.y - len);
                    g.lineTo(pt.x, pt.y + len);
                }
            }
        }
    }

    terminate() {
        if (this._starLayerContainers) {
            for (const container of this._starLayerContainers) {
                this.removeChild(container);
            }
            this._starLayerContainers = null;
            this._starLayers = null;
        }
        if (this._constellationGraphics) {
            this.removeChild(this._constellationGraphics);
            this._constellationGraphics.destroy();
            this._constellationGraphics = null;
            this._constellationLines = null;
        }
        if (this._animatedGradient) {
            this.removeChild(this._animatedGradient);
            this._animatedGradient = null;
        }
        if (super.terminate) super.terminate();
    }
}

//=============================================================================
window.Scene_SkillTree = Scene_SkillTree;
//=============================================================================

$dataSkillTrees = null;

(function ($) {
    // CONFIG:

    //=============================================================================
    // Create functions specific for my code if it does not already exist!
    // WARNING: DO NOT EDIT BELOW THIS LINE!!!
    //=============================================================================

    //-----------------------------------------------------------------------------
    Chaucer.parseArgs =
        Chaucer.parseArgs ||
        function (args) {
            // compare the current version with the target version.
            //-----------------------------------------------------------------------------

            const obj = {};
            for (var i = 0, l = args.length; i < l; i += 2) {
                obj[args[i]] = args[i + 1];
            }

            return obj;
        };

    //-----------------------------------------------------------------------------
    Chaucer.compareVersion =
        Chaucer.compareVersion ||
        function (current, target) {
            // compare the current version with the target version.
            //-----------------------------------------------------------------------------

            const v1 = current.split('.');
            const v2 = target.split('.');
            for (let i = 0, l = v1.length; i < l; i++) {
                if (v1[i] < v2[i]) return -1; // version is lower!
                if (v1[i] > v2[i]) return 1; // version is higher!
            }
            return 0; // same version!
        };

    //-----------------------------------------------------------------------------
    Chaucer.parse =
        Chaucer.parse ||
        function (data) {
            // recursively parse any data passed in.
            //-----------------------------------------------------------------------------
            try {
                data = JSON.parse(data);
            } catch (err) {
                data = data;
            } finally {
                if (typeof data === 'object') {
                    for (const key in data) {
                        data[key] = Chaucer.parse(data[key]);
                    }
                }
            }

            return data;
        };

    //-----------------------------------------------------------------------------
    Chaucer.makePluginInfo =
        Chaucer.makePluginInfo ||
        function ($, n) {
            // Create plugin info for the object provided.
            //-----------------------------------------------------------------------------

            for (var i = 0, l = $plugins.length; i < l; i++) {
                if (!$plugins[i].description.match(n)) continue;

                $.author = 'Chaucer';
                $.name = RegExp.$1;
                $.version = RegExp.$2;
                $.pluginName = $plugins[i].name;
                $.params = Chaucer.parse($plugins[i].parameters);
                $.commands = {};
                $.alias = {};
            }
        };

    //============================================================================
    //Create plugin information.
    //============================================================================

    const identifier = /(Rosedale Skill Tree) : Version - (\d+.\d+.\d+)/;
    // $._nameError = 'Rosedale Skill Tree was unable to load! Please revert any changes back to normal!';

    Chaucer.makePluginInfo($, identifier);

    if (!$.name) throw new Error($._nameError);

    //=============================================================================

    //-----------------------------------------------------------------------------
    $.registerPluginCommand = function (command, fn) {
        // compare the current version with the target version.
        //-----------------------------------------------------------------------------

        if (Utils.RPGMAKER_NAME === 'MV') $.commands[command] = fn;
        else if (Utils.RPGMAKER_NAME === 'MZ')
            PluginManager.registerCommand($.pluginName, command, fn);
    };

    //-----------------------------------------------------------------------------
    $.alias = function (className, method, fn, isStatic) {
        // use this method to quickly alias a method of a particular class.
        //-----------------------------------------------------------------------------

        let key = `${className.name}.${(isStatic ? '' : 'prototype.') + method}`;
        let object = isStatic ? className : className.prototype;

        if ($.alias[key]) throw new Error(`${key} already aliased!`);

        $.alias[key] = object[method];

        let fnString = fn.toString();
        let instances = fnString.match(/\$.alias\((.*?)\)/g) || [];

        for (let i = 0, len = instances.length; i < len; i++) {
            let old = instances[i];
            let args = ['this'].concat(old.match(/\((.*?)\)/)[1].split(','));
            args = args.filter(n => !!n);
            let next = `$.alias["${key}"].call(` + args.join(',') + ')';

            fnString = fnString.replace(old, next);
        }

        eval(`${key} = ` + fnString);
    };

    //-----------------------------------------------------------------------------
    $.expand = function (className, method, fn, isStatic) {
        // use this method to quickly alias a method of a particular class.
        //-----------------------------------------------------------------------------

        const obj = isStatic ? className : className.prototype;
        obj[method] = fn;
    };

    //=============================================================================
    // MV SPECIFIC CODE :
    //=============================================================================

    if (Utils.RPGMAKER_NAME === 'MV') {
        //-----------------------------------------------------------------------------
        $.alias(Game_Interpreter, 'pluginCommand', function (command, args) {
            //-----------------------------------------------------------------------------

            $.alias(command, args);

            command = command.toLowerCase();
            if ($.commands[command]) {
                $.commands[command].call(this, Chaucer.parseArgs(args));
            }
        });
    }

    //=============================================================================
    // ALIASED CODE BELOW THIS LINE!
    //=============================================================================

    //-----------------------------------------------------------------------------
    $.registerPluginCommand('start_skill_tree', function (args) {
        // register command for start_skill_tree.
        //-----------------------------------------------------------------------------

        $gameParty._menuActorId = Number(args.actor);
        SceneManager.push(Scene_SkillTree);
    });

    //=============================================================================
    // Game_Actor :
    //=============================================================================

    Object.defineProperties(Game_Actor.prototype, {
        sp: {
            get: function () {
                return this._sp + this._bonusSp;
            },
            configurable: true,
        },
    });

    //-----------------------------------------------------------------------------
    $.alias(
        Game_Actor,
        'initMembers',
        function () {
            // Aliased initMembers of class Game_Actor.
            //-----------------------------------------------------------------------------

            $.alias();
            this._sp = 0;
            this._usedSp = 0;
            this._bonusSp = 0;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'skillTree',
        function () {
            // return the currently associated skill tree.
            //-----------------------------------------------------------------------------

            return $dataSkillTrees[this._actorId] || null;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'totalSp',
        function () {
            // return the total amount of sp this actor has.
            //-----------------------------------------------------------------------------

            return this.sp;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'usedSp',
        function () {
            // return the total amount of USED sp.
            //-----------------------------------------------------------------------------

            return this._usedSp;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'unusedSp',
        function () {
            // return the amount of sp that is NOT used.
            //-----------------------------------------------------------------------------

            return this.sp - this._usedSp;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.alias(
        Game_Actor,
        'setup',
        function (actorId) {
            // Aliased setup of class Game_Actor.
            //-----------------------------------------------------------------------------

            $.alias(actorId);
            this.initSp();
            this._skillTreeProgress = this._skillTreeProgress || [];
            // Lock in the starting class for the skill tree, only if not already set
            if (!this._lockedSkillTreeClassId) this._lockedSkillTreeClassId = this._classId;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'initSp',
        function () {
            // initialize SP amount.
            //-----------------------------------------------------------------------------

            let params = Chaucer.skillTree.params;
            let points = params.pointsPerLevel;

            if (this.level % params.bonusPointLevels == 0) {
                points += params.pointsPerLevel;
            }

            this._sp = Math.max(points, this._sp);
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.alias(
        Game_Actor,
        'levelUp',
        function () {
            // Aliased levelUp of class Game_Actor.
            //-----------------------------------------------------------------------------

            $.alias();
            let params = Chaucer.skillTree.params;
            let points = params.pointsPerLevel * this.level;

            if (this.level % params.bonusPointLevels == 0) {
                points += params.pointsPerLevel;
            }

            this._sp = Math.max(points, this._sp);
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'isSkillInCurrentTree',
        function (id) {
            // return if the provided skill id is in the current skill tree.
            //-----------------------------------------------------------------------------

            const tree = this.skillTree();

            if (tree) {
                return [].concat
                    .apply([], tree)
                    .filter(node => !!node)
                    .some(n => n.skillId == id);
            }
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Game_Actor,
        'removeUsedSp',
        function (id) {
            // remove the used sp from the skilll being removed.
            //-----------------------------------------------------------------------------

            const tree = this.skillTree();

            if (tree) {
                const node = [].concat
                    .apply([], tree)
                    .filter(node => !!node)
                    .find(n => n.skillId == id);
                if (node) this._usedSp -= Number(node.cost);
            }
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.alias(
        Game_Actor,
        'forgetSkill',
        function (skillId) {
            // Aliased forgetSkill of class Game_Actor.
            //-----------------------------------------------------------------------------

            $.alias(skillId);
            if (this.isSkillInCurrentTree(skillId)) {
                this.removeUsedSp(skillId);
            }
        },
        false
    );

    //=============================================================================
    // Bitmap :
    //=============================================================================

    //-----------------------------------------------------------------------------
    $.expand(
        Bitmap,
        'clearCircle',
        function (x, y, radius) {
            // clear a circle.
            //-----------------------------------------------------------------------------

            const context = this.context;

            context.save();
            context.globalCompositeOperation = 'destination-out';
            context.beginPath();
            context.arc(x, y, radius, 0, 2 * Math.PI);
            context.fillStyle = 'white';
            context.fill();
            context.closePath();
            context.restore();

            context.globalCompositeOperation = 'source-over';
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Bitmap,
        'clearRoundedRect',
        function (x, y, width, height, radius) {
            // Aliased drawRoundedRect of class Bitmap.
            //-----------------------------------------------------------------------------

            const circ = radius * 2;

            for (let i = 0, l = 4; i < l; i++) {
                const ox = radius + (width - circ) * (i % 2);
                const oy = radius + Math.floor(i / 2) * (height - circ);

                this.clearCircle(x + ox, y + oy, radius);
            }

            this.clearRect(x + radius, y, width - circ, height);
            this.clearRect(x, y + radius, width, height - circ);
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Bitmap,
        'drawRoundedRect',
        function (x, y, width, height, radius, color) {
            // Aliased drawRoundedRect of class Bitmap.
            //-----------------------------------------------------------------------------

            const circ = radius * 2;

            for (let i = 0, l = 4; i < l; i++) {
                const ox = radius + (width - circ) * (i % 2);
                const oy = radius + Math.floor(i / 2) * (height - circ);

                this.drawCircle(x + ox, y + oy, radius, color);
            }

            this.clearRect(radius, y, width - circ, height);
            this.clearRect(x, radius, width, height - circ);
            this.fillRect(radius, y, width - circ, height, color);
            this.clearRect(x, radius, width, height - circ);
            this.fillRect(x, radius, width, height - circ, color);
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Bitmap,
        'drawArrow',
        function (fromX, fromY, toX, toY, width, color = '#ff0000') {
            // Definition.
            //-----------------------------------------------------------------------------

            const context = this.context;
            const headSize = 4;
            var angle = Math.atan2(toY - fromY, toX - fromX);

            toX -= Math.cos(angle) * (width * 1.15);
            toY -= Math.sin(angle) * (width * 1.15);

            context.save();

            context.strokeStyle = color;
            context.lineWidth = width;
            context.fillStyle = color;

            context.beginPath();
            context.moveTo(fromX, fromY);
            context.lineTo(toX, toY);
            context.stroke();

            context.beginPath();

            const x0 = toX - headSize * Math.cos(angle - Math.PI / 7);
            const y0 = toY - headSize * Math.sin(angle - Math.PI / 7);

            const x1 = toX - headSize * Math.cos(angle + Math.PI / 7);
            const y1 = toY - headSize * Math.sin(angle + Math.PI / 7);

            context.moveTo(toX, toY);
            context.lineTo(x0, y0);
            context.lineTo(x1, y1);
            context.lineTo(toX, toY);
            context.lineTo(x0, y0);

            context.stroke();
            context.fill();

            context.restore();

            this._baseTexture.update();
        },
        false
    );

    //=============================================================================
    // DataManager :
    //=============================================================================

    //-----------------------------------------------------------------------------
    $.alias(
        DataManager,
        'loadDatabase',
        function () {
            // Aliased loadDatabase of class DataManager.
            //-----------------------------------------------------------------------------

            $.alias();
            const name = '$dataSkillTrees';
            const src = 'skilltree/SkillTree.json';

            this.loadDataFile(name, src);
        },
        true
    );

    //-----------------------------------------------------------------------------
    $.alias(
        DataManager,
        'onXhrError',
        function (name, src, url) {
            // Aliased onXhrError of class DataManager.
            //-----------------------------------------------------------------------------

            if (src == 'skilltree/SkillTree.json') {
                const fs = require('fs');

                if (fs) {
                    if (!fs.existsSync('data/skilltree')) fs.mkdirSync('data/skilltree');
                    fs.writeFileSync(`data/${src}`, JsonEx.stringify([null]));

                    this.loadDataFile(name, src);
                }
            } else {
                $.alias();
            }
        },
        true
    );

    if (Utils.RPGMAKER_NAME == 'MV') {
        DataManager.loadDataFile = function (name, src) {
            var xhr = new XMLHttpRequest();
            var url = 'data/' + src;
            xhr.open('GET', url);
            xhr.overrideMimeType('application/json');
            xhr.onload = function () {
                if (xhr.status < 400) {
                    window[name] = JSON.parse(xhr.responseText);
                    DataManager.onLoad(window[name]);
                }
            };

            xhr.onerror =
                this._mapLoader ||
                function () {
                    if (src == 'skilltree/SkillTree.json') {
                        const fs = require('fs');
                        if (fs) {
                            if (!fs.existsSync('data/skilltree')) fs.mkdirSync('data/skilltree');
                            fs.writeFileSync(`data/${src}`, JsonEx.stringify([null]));

                            DataManager.loadDataFile(name, src);
                        }
                    } else {
                        DataManager._errorUrl = DataManager._errorUrl || url;
                    }
                };

            window[name] = null;
            xhr.send();
        };
    }

    //=============================================================================
    // Scene_Menu :
    //=============================================================================

    //-----------------------------------------------------------------------------
    $.alias(
        Scene_Menu,
        'createCommandWindow',
        function () {
            // Aliased createCommandWindow of class Scene_Menu.
            //-----------------------------------------------------------------------------

            $.alias();
            this._commandWindow.setHandler('skill_tree', this.commandPersonal.bind(this));
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.alias(
        Scene_Menu,
        'onPersonalOk',
        function () {
            // Aliased onPersonalOk of class Scene_Menu.
            //-----------------------------------------------------------------------------

            if (this._commandWindow.currentSymbol() == 'skill_tree') {
                // Set the selected actor for the skill tree
                const actor = this._statusWindow.actor();
                if (actor) {
                    $gameParty._menuActorId = actor.actorId();
                }
                SceneManager.push(Scene_SkillTree);
            } else {
                $.alias();
            }
        },
        false
    );

    //=============================================================================
    // Window_Base :
    //=============================================================================

    if (Utils.RPGMAKER_NAME === 'MV') {
        //-----------------------------------------------------------------------------
        $.alias(
            Window_Base,
            'initialize',
            function (x, y, width, height) {
                // Aliased initialize of class Window_Base.
                //-----------------------------------------------------------------------------

                if (typeof x === 'object') {
                    height = x.height;
                    width = x.width;
                    y = x.y;
                    x = x.x;
                }

                $.alias(x, y, width, height);
            },
            false
        );
    }

    //=============================================================================
    // VisuStella MainMenuCore Integration :
    //=============================================================================

    //-----------------------------------------------------------------------------
    // Auto-add Scene_SkillTree to VisuStella ActorBgMenus for actor background support
    //-----------------------------------------------------------------------------
    if (typeof VisuMZ !== 'undefined' && VisuMZ.MainMenuCore && VisuMZ.MainMenuCore.Settings) {
        const actorBgMenus = VisuMZ.MainMenuCore.Settings.General.ActorBgMenus;
        if (actorBgMenus && !actorBgMenus.includes('Scene_SkillTree')) {
            actorBgMenus.push('Scene_SkillTree');
        }
    }

    //-----------------------------------------------------------------------------
    // Add VisuStella command list entry for better integration
    //-----------------------------------------------------------------------------
    if (typeof VisuMZ !== 'undefined' && VisuMZ.MainMenuCore && Window_MenuCommand._commandList) {
        // Add skill tree command to VisuStella's command list if not already present
        const skillTreeCommand = {
            Symbol: 'skill_tree',
            Icon: 0,
            TextStr: $.params.menuName || 'Skill Tree',
            TextJS: function () {
                return $.params.menuName || 'Skill Tree';
            },
            ShowJS: function () {
                return $.params.inMenu;
            },
            EnableJS: function () {
                const switchId = $.params.enableSwitch;
                return switchId ? $gameSwitches.value(switchId) : true;
            },
            ExtJS: function () {
                return null;
            },
            CallHandlerJS: function () {
                SceneManager.push(Scene_SkillTree);
            },
            PersonalHandlerJS: function () {
                const actor = this.actor();
                if (actor) {
                    $gameParty._menuActorId = actor.actorId();
                }
                SceneManager.push(Scene_SkillTree);
            },
        };

        // Check if skill tree command already exists in the list
        const existingCommand = Window_MenuCommand._commandList.find(
            cmd => cmd.Symbol === 'skill_tree'
        );
        if (!existingCommand) {
            Window_MenuCommand._commandList.push(skillTreeCommand);
        }
    }

    //=============================================================================
    // Window_MenuCommand :
    //=============================================================================

    //-----------------------------------------------------------------------------
    $.alias(
        Window_MenuCommand,
        'addOriginalCommands',
        function () {
            // Aliased addOriginalCommands of class Window_MenuCommand.
            //-----------------------------------------------------------------------------

            $.alias();
            if (this.needsCommand('skill_tree')) {
                const enabled = this.isSkillTreeEnabled();
                const name = $.params.menuName;

                // Add as personal command for VisuStella compatibility
                this.addCommand(name, 'skill_tree', enabled, null, 'skill_tree');
            }
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.expand(
        Window_MenuCommand,
        'isSkillTreeEnabled',
        function () {
            // return if the skill tree is enabled.
            //-----------------------------------------------------------------------------

            const switchId = $.params.enableSwitch;

            if (switchId) return $gameSwitches.value(switchId);
            return true;
        },
        false
    );

    //-----------------------------------------------------------------------------
    $.alias(
        Window_MenuCommand,
        'needsCommand',
        function (name) {
            // Aliased needsCommand of class Window_MenuCommand.
            //-----------------------------------------------------------------------------

            if (name == 'skill_tree') {
                return $.params.inMenu;
            }

            return $.alias(name);
        },
        false
    );

    //=============================================================================
})(Chaucer.skillTree);
//=============================================================================

//=============================================================================
// Star Particle Sprite
//=============================================================================
class Sprite_StarParticle extends Sprite {
    constructor() {
        super();
        const STAR_MIN_SIZE = 2,
            STAR_MAX_SIZE = 5;
        this._size = Math.random() * (STAR_MAX_SIZE - STAR_MIN_SIZE) + STAR_MIN_SIZE;
        this.bitmap = new Bitmap(this._size, this._size);
        this.bitmap.fillRect(0, 0, this._size, this._size, 'white');
        this.x = Math.random() * Graphics.width;
        this.y = Math.random() * Graphics.height;
        this._fadeIn = Math.random() < 0.5;
        this.opacity = Math.random() * 255;
    }
    update() {
        super.update();
        const STAR_FADE_SPEED = 0.01;
        if (this._fadeIn) {
            this.opacity += STAR_FADE_SPEED * 255;
            if (this.opacity >= 255) {
                this.opacity = 255;
                this._fadeIn = false;
            }
        } else {
            this.opacity -= STAR_FADE_SPEED * 255;
            if (this.opacity <= 0) {
                this.opacity = 0;
                this._fadeIn = true;
            }
        }
    }
}
//=============================================================================

//=============================================================================
// Parallax Star Sprite
//=============================================================================
class Sprite_ParallaxStar extends Sprite {
    constructor(
        minSize,
        maxSize,
        minAlpha,
        maxAlpha,
        driftSpeed,
        color,
        wobble,
        x,
        y,
        isRare,
        sizeBoost = 0,
        alphaBoost = 0,
        isSuperGiant = false,
        isSuperCore = false
    ) {
        super();
        let size = minSize + Math.random() * (maxSize - minSize) + sizeBoost;
        this._color = color;
        if (isRare) size += 1;
        // --- Special Galaxy Effects ---
        if (this._isSpecialGalaxy) {
            size *= 5; // 5x size for elemental and seasonal suns
        }
        if (isSuperGiant) {
            size *= 6; // Dramatically larger for scary huge supergiants
        }
        this._size = size;
        this.bitmap = new Bitmap(this._size * 2, this._size * 2); // Double size for rings

        // Determine if this should be a planet (now 30% chance for rare stars)
        this._isPlanet = isRare && Math.random() < 0.3;
        this._isSuperCore = isSuperCore;

        if (this._isSuperCore) {
            // Use a much larger bitmap for the sun's glow
            let bigSize = this._size * 3;
            if (this._isSpecialGalaxy) bigSize = this._size * 4; // Even larger for special
            this.bitmap = new Bitmap(bigSize, bigSize);
            this._drawSuperCore(
                this.bitmap,
                this._size,
                this._color,
                bigSize / 2,
                bigSize / 2,
                this._isSpecialGalaxy,
                this._specialColor
            );
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            this.x += this._size; // Center the sprite
            this.y += this._size;
            this._isVortex = false;
        } else if (this._isPlanet) {
            // Use a much larger bitmap for the planet's rings
            let bigSize = this._size * 3;
            if (isSuperGiant) bigSize = this._size * 4; // Even larger for supergiants
            this.bitmap = new Bitmap(bigSize, bigSize);
            this._drawPlanet(this.bitmap, this._size, this._color, bigSize / 2, bigSize / 2);
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            this.x += this._size;
            this.y += this._size;
            this._isVortex = false;
        } else {
            // Shape selection with probabilities (no square)
            const shapeRoll = Math.random();
            if (isSuperGiant) {
                // Only allow circle, star, or hexagon for supergiants
                if (shapeRoll < 0.12) {
                    this._drawStarShape(this.bitmap, this._size, this._color);
                } else if (shapeRoll < 0.24) {
                    this._drawHexagonShape(this.bitmap, this._size, this._color);
                } else {
                    this._drawCircleShape(this.bitmap, this._size, this._color);
                }
            } else {
                if (shapeRoll < 0.08) {
                    this._drawStarShape(this.bitmap, this._size, this._color);
                } else if (shapeRoll < 0.18) {
                    this._drawDiamondShape(this.bitmap, this._size, this._color);
                } else if (shapeRoll < 0.28) {
                    this._drawHexagonShape(this.bitmap, this._size, this._color);
                } else {
                    this._drawCircleShape(this.bitmap, this._size, this._color);
                }
            }
        }

        this._origX = typeof x === 'number' ? x : Math.random() * Graphics.width;
        this._origY = typeof y === 'number' ? y : Math.random() * Graphics.height;
        this.x = this._origX;
        this.y = this._origY;
        this._driftSpeed = driftSpeed * (Math.random() * 0.5 + 0.75);
        let baseAlpha = minAlpha + Math.random() * (maxAlpha - minAlpha) + alphaBoost;
        if (isRare) baseAlpha = Math.min(baseAlpha + 25, 255); // Reduced from 40 to 25
        this._baseAlpha = Math.min(baseAlpha, 255);
        // If supergiant, lower opacity for softness
        if (isSuperGiant) this._baseAlpha = Math.min(baseAlpha, 90 + Math.random() * 30);
        this.opacity = this._baseAlpha;
        this._twinkleSpeed = 0.012 + Math.random() * 0.025; // Increased twinkle speed range
        this._twinklePhase = Math.random() * Math.PI * 2;
        this._twinkleStrength = 0.5 + Math.random() * 0.7; // Increased twinkle strength range
        this._wobble = wobble;
        if (wobble) {
            this._wobblePhase = Math.random() * Math.PI * 2;
            this._wobbleAmp = 1 + Math.random() * 1.5; // Reduced from 2-5 to 1-2.5
            this._wobbleSpeed = 0.01 + Math.random() * 0.01;
        }
        // Spin
        if (isSuperGiant) {
            this._spinSpeed = 0; // No spin for supergiant
        } else {
            this._spinSpeed = (Math.random() - 0.5) * 0.04; // Slow spin for all others
        }
        this.rotation = Math.random() * Math.PI * 2;
        if (this._isPlanet && this._size < 8) {
            this._size += 2 + Math.random() * 2; // bump up small planets by 2-4 px
            this.bitmap = new Bitmap(this._size * 2, this._size * 2); // update bitmap size for rings
        }
        if (this._isPlanet && this._isGiantPlanet) {
            this._size = this._size * 2.5; // was 1.2, now 2.5 for truly gigantic
        }
        // For supergiant planets, use the galaxy color scheme if available
        if (isSuperGiant && this._isPlanet) {
            if (
                this._solarSystemPalette &&
                Array.isArray(this._solarSystemPalette) &&
                this._solarSystemPalette.length > 0
            ) {
                this._color = this._solarSystemPalette[0]; // Use the main galaxy color for the core
                this._ringPalette = this._solarSystemPalette.slice(1); // Use the rest for rings
            } else if (this._specialColor) {
                this._color = this._specialColor;
                this._ringPalette = [this._specialColor];
            } else {
                // fallback: keep previous color logic
                this._ringPalette = ['#ffe066', '#66aaff', '#ff66cc'];
            }
        }
        // --- Orbiting Moons for Drifting Supergiants ---
        if (isSuperGiant && this._isPlanet) {
            this._moons = [];
            const moonCount = 2 + Math.floor(Math.random() * 3); // 2-4 moons
            for (let m = 0; m < moonCount; m++) {
                const moon = new Sprite();
                const moonColor =
                    this._ringPalette && this._ringPalette.length > 0
                        ? this._ringPalette[m % this._ringPalette.length]
                        : this._color;
                const moonBmp = new Bitmap(12, 12);
                moonBmp.drawCircle(6, 6, 4, moonColor);
                moon.bitmap = moonBmp;
                moon.anchor.x = 0.5;
                moon.anchor.y = 0.5;
                moon._orbitRadius = this._size * (1.1 + Math.random() * 0.5);
                moon._orbitSpeed = 0.002 + Math.random() * 0.001;
                moon._orbitPhase = Math.random() * Math.PI * 2;
                this._moons.push(moon);
                this.addChild(moon);
            }
        }
    }

    _drawStarShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size / 2;
        const cy = size / 2;
        const spikes = 5 + Math.floor(Math.random() * 2); // 5 or 6 points
        const outerRadius = size / 2;
        const innerRadius = size / 2.7;
        ctx.save();
        ctx.beginPath();
        for (let i = 0; i < spikes * 2; i++) {
            const angle = (Math.PI / spikes) * i;
            const r = i % 2 === 0 ? outerRadius : innerRadius;
            ctx.lineTo(cx + Math.cos(angle) * r, cy + Math.sin(angle) * r);
        }
        ctx.closePath();
        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawDiamondShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size / 2;
        const cy = size / 2;
        const radius = size / 2;

        ctx.save();
        ctx.beginPath();
        ctx.moveTo(cx, cy - radius);
        ctx.lineTo(cx + radius, cy);
        ctx.lineTo(cx, cy + radius);
        ctx.lineTo(cx - radius, cy);
        ctx.closePath();

        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawCrossShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size / 2;
        const cy = size / 2;
        const armLength = size / 2;
        const armWidth = size / 4;

        ctx.save();
        ctx.beginPath();
        // Vertical arm
        ctx.rect(cx - armWidth / 2, cy - armLength, armWidth, armLength * 2);
        // Horizontal arm
        ctx.rect(cx - armLength, cy - armWidth / 2, armLength * 2, armWidth);

        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawHexagonShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size / 2;
        const cy = size / 2;
        const radius = size / 2;

        ctx.save();
        ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (Math.PI / 3) * i;
            const x = cx + radius * Math.cos(angle);
            const y = cy + radius * Math.sin(angle);
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
        }
        ctx.closePath();

        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawTriangleShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size / 2;
        const cy = size / 2;
        const radius = size / 2;

        ctx.save();
        ctx.beginPath();
        for (let i = 0; i < 3; i++) {
            const angle = ((Math.PI * 2) / 3) * i - Math.PI / 2;
            const x = cx + radius * Math.cos(angle);
            const y = cy + radius * Math.sin(angle);
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
        }
        ctx.closePath();

        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawCircleShape(bitmap, size, color) {
        const ctx = bitmap.context;
        const cx = size;
        const cy = size;
        ctx.save();
        ctx.beginPath();
        ctx.arc(cx, cy, size / 2, 0, Math.PI * 2);
        ctx.closePath();
        ctx.fillStyle = color;
        ctx.globalAlpha = 1.0;
        ctx.shadowColor = color;
        ctx.shadowBlur = Math.max(2, size * 0.3);
        ctx.fill();
        ctx.restore();
        bitmap._baseTexture.update();
    }

    _drawPlanet(bitmap, size, color, cx, cy) {
        const ctx = bitmap.context;

        // Draw planet atmosphere glow
        ctx.save();
        const gradient = ctx.createRadialGradient(cx, cy, size * 0.4, cx, cy, size * 0.6);
        gradient.addColorStop(0, color);
        gradient.addColorStop(1, 'rgba(0,0,0,0)');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(cx, cy, size * 0.6, 0, Math.PI * 2);
        ctx.fill();

        // Draw planet core
        ctx.beginPath();
        const coreRadius = this._isGiantPlanet ? size * 1.2 : size * 0.4;
        ctx.arc(cx, cy, coreRadius, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // Draw rings (if planet is large enough)
        if (size > 4) {
            // Always use a hex color for rings (fallback to gold if not hex)
            let ringHex = color;
            if (typeof color !== 'string' || color[0] !== '#') ringHex = '#ffe066';
            // --- BEGIN GIANT PLANET RING LOGIC ---
            const ringCount = this._isGiantPlanet
                ? 5 + Math.floor(Math.random() * 3)
                : 2 + Math.floor(Math.random() * 3); // 5-7 rings for giants
            // Bright, glowy, high-contrast palette for rings
            const ringPalette = [
                '#ffffff', // white
                '#ffe066', // gold
                '#33ffe6', // neon cyan
                '#ff66cc', // hot pink
                '#99ccff', // electric blue
                '#ffb347', // bright orange
                '#baffc9', // mint
                '#ffff66', // lemon yellow
                '#ff6666', // neon red
                '#b266ff', // vivid purple
            ];
            for (let i = 0; i < ringCount; i++) {
                const ringWidth = this._isGiantPlanet
                    ? size * (0.05 + Math.random() * 0.03)
                    : size * (0.04 + Math.random() * 0.02); // much thicker for giants
                const ringRadius = this._isGiantPlanet
                    ? size * (1.0 + i * 0.28)
                    : size * (0.8 + i * 0.24); // much larger for giants
                let ringAlpha = 0.75;
                if (this._isGiantPlanet) {
                    // Brighter rings for supergiant drifters (scary huge planets)
                    if (size >= 60) {
                        ringAlpha = 0.85; // much brighter for supergiants
                    } else {
                        ringAlpha = 0.95; // brighter for regular giants
                    }
                } else if (
                    ringPalette[i].toLowerCase() !== '#ffffff' &&
                    ringPalette[i].toLowerCase() !== 'white'
                ) {
                    ringAlpha = 0.9; // brighter for regular planets
                }
                const ringColor = ringPalette[Math.floor(Math.random() * ringPalette.length)];
                ctx.save();
                ctx.beginPath();
                ctx.ellipse(cx, cy, ringRadius, ringRadius * 0.2, Math.PI / 4, 0, Math.PI * 2);
                ctx.strokeStyle = `rgba(${this._hexToRgb(ringColor)},${ringAlpha})`;
                ctx.lineWidth = ringWidth;
                ctx.shadowColor = ringColor;
                ctx.shadowBlur = this._isGiantPlanet ? 64 + size * 1.5 : 24 + size * 0.8; // increased glow for all planets
                ctx.stroke();
                ctx.restore();
                // Add ring glow
                const ringGradient = ctx.createRadialGradient(
                    cx,
                    cy,
                    ringRadius - ringWidth / 2,
                    cx,
                    cy,
                    ringRadius + ringWidth / 2
                );
                ringGradient.addColorStop(
                    0,
                    `rgba(${this._hexToRgb(ringColor)},${ringAlpha * (this._isGiantPlanet ? 1.2 : 0.9)})`
                ); // increased glow intensity
                ringGradient.addColorStop(1, 'rgba(0,0,0,0)');
                // ... existing code ...
            }
            // --- END GIANT PLANET RING LOGIC ---
        }

        // Add atmospheric highlights
        ctx.beginPath();
        ctx.arc(cx - size * 0.2, cy - size * 0.2, size * 0.1, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255,255,255,0.3)';
        ctx.fill();

        ctx.restore();
        bitmap._baseTexture.update();
    }

    _hexToRgb(color) {
        // Only handle hex colors, otherwise fallback to white
        if (typeof color === 'string' && color[0] === '#') {
            let hex = color.replace('#', '');
            if (hex.length === 3) {
                hex = hex
                    .split('')
                    .map(x => x + x)
                    .join('');
            }
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            if (!isNaN(r) && !isNaN(g) && !isNaN(b)) {
                return `${r},${g},${b}`;
            }
        }
        // Fallback: white
        return '255,255,255';
    }

    update() {
        super.update();
        // Orbiters (not micro, not legendary)
        if (this._isOrbital) {
            if (!this._updateFrame) this._updateFrame = 0;
            this._updateFrame = (this._updateFrame + 1) % 2;
            if (this._updateFrame !== 0) return;
            // Update orbital position
            this._orbitPhase += this._orbitSpeed;
            let x = this._orbitCore ? this._orbitCore._solarSystemCenter.x : this._orbitCenter.x;
            let y = this._orbitCore ? this._orbitCore._solarSystemCenter.y : this._orbitCenter.y;
            x += Math.cos(this._orbitPhase) * this._orbitRadius;
            y += Math.sin(this._orbitPhase) * this._orbitRadius * 0.38;
            // Add orbital wobble if enabled
            if (this._orbitWobble) {
                const wobble =
                    Math.sin(this._orbitPhase * this._orbitWobbleSpeed) * this._orbitWobbleAmp;
                x += wobble;
                y += wobble;
            }
            this.x = x;
            this.y = y;
            // Rotate rings for planets
            if (this._isPlanet) {
                this.rotation += this._orbitSpeed * 0.5;
            }
        } else if (this._isSolarSystemCore) {
            // Always update micro orbiters every frame
            if (this._microOrbiters) {
                if (!this._microOrbiterFrame) this._microOrbiterFrame = 0;
                this._microOrbiterFrame = (this._microOrbiterFrame + 1) % 2;
                if (this._microOrbiterFrame === 0) {
                    for (const dot of this._microOrbiters) {
                        dot._orbitAngle += dot._orbitSpeed;
                        const cx = this._solarSystemCenter.x;
                        const cy = this._solarSystemCenter.y;
                        dot.x = cx + Math.cos(dot._orbitAngle) * dot._orbitRadius;
                        dot.y = cy + Math.sin(dot._orbitAngle) * dot._orbitRadius * 0.38;
                    }
                }
            }

            // Solar flare system
            this.updateSolarFlares();
        } else {
            // Foreground drifter stars (front layer): frame skip
            if (this._driftSpeed >= 0.5) {
                if (!this._updateFrame) this._updateFrame = 0;
                this._updateFrame = (this._updateFrame + 1) % 2;
                if (this._updateFrame !== 0) return;
            }
            // Regular movement
            this.x += this._driftSpeed;
            if (this.x > Graphics.width) {
                this.x = 0;
                this.y = Math.random() * Graphics.height;
            }
        }
        // ... rest of update code (twinkle, wobble, spin, etc.) ...
        // Add slow rotation for supergiant planets (not spin)
        if (this._isPlanet && this._size >= 60) {
            this.rotation += 0.000025; // even slower, gentle rotation
        }
        // Animate orbiting moons for supergiants
        if (this._moons) {
            for (const moon of this._moons) {
                moon._orbitPhase += moon._orbitSpeed;
                moon.x = this.width / 2 + Math.cos(moon._orbitPhase) * moon._orbitRadius;
                moon.y = this.height / 2 + Math.sin(moon._orbitPhase) * moon._orbitRadius * 0.7;
            }
        }
    }

    _drawSuperCore(bitmap, size, color, cx, cy, isSpecial = false, specialColor = null) {
        const ctx = bitmap.context;
        // Pulsing halo
        const t = performance.now() / 1000;
        let pulse = 0.8 + 0.2 * Math.sin(t * 2.2 + Math.random());
        if (isSpecial) pulse = 1.0 + 0.3 * Math.sin(t * 3.2 + Math.random()); // Stronger pulse
        let haloRadius = size * 0.95 * pulse;
        if (isSpecial) haloRadius = size * 1.2 * pulse;
        ctx.save();
        ctx.beginPath();
        ctx.arc(cx, cy, haloRadius, 0, Math.PI * 2);
        ctx.closePath();
        ctx.globalAlpha = isSpecial ? 0.55 + 0.25 * pulse : 0.38 + 0.18 * pulse;
        ctx.fillStyle = isSpecial ? specialColor || color : color;
        ctx.shadowColor = isSpecial ? specialColor || color : color;
        ctx.shadowBlur = isSpecial ? size * 1.2 : size * 0.7;
        ctx.fill();
        ctx.restore();
        // Core
        ctx.save();
        ctx.beginPath();
        ctx.arc(cx, cy, size * 0.45, 0, Math.PI * 2);
        ctx.closePath();
        ctx.globalAlpha = 1.0;
        ctx.fillStyle = isSpecial ? specialColor || color : color;
        ctx.shadowColor = isSpecial ? specialColor || color : color;
        ctx.shadowBlur = isSpecial ? size * 0.5 : size * 0.25;
        ctx.fill();
        ctx.restore();
        // Optionally, add a faint secondary aura
        ctx.save();
        ctx.beginPath();
        ctx.arc(cx, cy, size * (isSpecial ? 1.0 : 0.7), 0, Math.PI * 2);
        ctx.closePath();
        ctx.globalAlpha = isSpecial ? 0.22 : 0.12;
        ctx.fillStyle = isSpecial ? specialColor || color : color;
        ctx.shadowColor = isSpecial ? specialColor || color : color;
        ctx.shadowBlur = isSpecial ? size * 1.8 : size * 1.2;
        ctx.fill();
        ctx.restore();
        // --- Outline/Halo ---
        if (isSpecial) {
            ctx.save();
            ctx.beginPath();
            ctx.arc(cx, cy, size * 0.65, 0, Math.PI * 2);
            ctx.closePath();
            ctx.lineWidth = 4;
            ctx.strokeStyle = specialColor || color;
            ctx.globalAlpha = 0.7;
            ctx.shadowColor = specialColor || color;
            ctx.shadowBlur = size * 0.8;
            ctx.stroke();
            ctx.restore();
        }
        bitmap._baseTexture.update();
    }

    updateSolarFlares() {
        if (!this._isSuperCore) return;

        // Initialize solar flare system
        if (!this._solarFlareTimer) {
            this._solarFlareTimer = 0;
            this._nextSolarFlareTime = 600 + Math.random() * 1200; // 10-30 seconds
            this._solarFlares = [];
        }

        this._solarFlareTimer++;

        // Create new solar flare
        if (this._solarFlareTimer >= this._nextSolarFlareTime) {
            this.createSolarFlare();
            this._solarFlareTimer = 0;
            this._nextSolarFlareTime = 300 + Math.random() * 900; // 5-20 seconds
        }

        // Update existing solar flares
        for (let i = this._solarFlares.length - 1; i >= 0; i--) {
            const flare = this._solarFlares[i];
            flare.update();

            if (flare._isFinished) {
                if (flare.parent) flare.parent.removeChild(flare);
                this._solarFlares.splice(i, 1);
            }
        }
    }

    createSolarFlare() {
        const flare = new Sprite_SolarFlare(this.x, this.y, this._size, this._color);
        this._solarFlares.push(flare);

        // Add to the same container as the star
        if (this.parent) {
            this.parent.addChild(flare);
        }
    }
}
//=============================================================================

//=============================================================================
// Star Burst Sprite
//=============================================================================
class Sprite_StarBurst extends Sprite {
    constructor(x, y, color = '#33ff99') {
        super();
        this._x = x;
        this._y = y;
        this._color = color;
        this._particles = [];
        this._frame = 0;
        this._duration = 60; // 1 second at 60fps
        this._burstCount = 24; // Number of particles in the burst
        this._spiralTightness = 0.2; // How tight the spiral is
        this._expansionSpeed = 3; // How fast particles move outward
        this._rotationSpeed = 0.1; // How fast the spiral rotates
        this._fadeStart = 30; // When particles start fading
        this._createParticles();
    }

    _createParticles() {
        for (let i = 0; i < this._burstCount; i++) {
            const particle = new Sprite();
            const size = 3 + Math.random() * 4; // 3-7px
            particle.bitmap = new Bitmap(size, size);
            particle.bitmap.fillRect(0, 0, size, size, this._color);
            particle.x = this._x;
            particle.y = this._y;
            particle.opacity = 255;
            particle.anchor.x = 0.5;
            particle.anchor.y = 0.5;
            particle.rotation = Math.random() * Math.PI * 2;
            particle._angle = (i / this._burstCount) * Math.PI * 2;
            particle._distance = 0;
            particle._spinSpeed = (Math.random() - 0.5) * 0.2;
            this._particles.push(particle);
            this.addChild(particle);
        }
    }

    update() {
        super.update();
        this._frame++;

        if (this._frame >= this._duration) {
            this.parent && this.parent.removeChild(this);
            return;
        }

        for (const particle of this._particles) {
            // Update position in spiral
            particle._angle += this._rotationSpeed;
            particle._distance += this._expansionSpeed;
            const spiralOffset = particle._distance * this._spiralTightness;
            particle.x = this._x + Math.cos(particle._angle + spiralOffset) * particle._distance;
            particle.y = this._y + Math.sin(particle._angle + spiralOffset) * particle._distance;

            // Update rotation
            particle.rotation += particle._spinSpeed;

            // Fade out
            if (this._frame > this._fadeStart) {
                const fadeProgress =
                    (this._frame - this._fadeStart) / (this._duration - this._fadeStart);
                particle.opacity = 255 * (1 - fadeProgress);
            }
        }
    }
}

// --- Add after createConstellationManager ---
Scene_SkillTree.prototype.createSupercoreLineManager = function () {
    this._supercoreLineAlpha = 0;
    this._supercoreLineState = 'idle';
    this._supercoreLineTimer = 0;
    this._supercoreLineDuration = 0;
    this._supercoreLinePairs = [];
    this._supercoreLineNextTime = 180 + Math.random() * 180; // 3-6s
    this.addChild(this._supercoreLineGraphics);
};

Scene_SkillTree.prototype.updateSupercoreLines = function () {
    if (!this._superCores || this._superCores.length < 2) return;
    if (this._supercoreLineState === 'idle') {
        this._supercoreLineTimer++;
        if (this._supercoreLineTimer >= this._supercoreLineNextTime) {
            this._supercoreLineTimer = 0;
            this._supercoreLineState = 'fadein';
            this._supercoreLineAlpha = 0;
            this._supercoreLineDuration = 60; // fade in 1s
            this._supercoreLinePairs = this._pickRandomSupercorePairs();
        }
    } else if (this._supercoreLineState === 'fadein') {
        this._supercoreLineAlpha += 1 / 60;
        if (this._supercoreLineAlpha >= 0.7) {
            this._supercoreLineAlpha = 0.7;
            this._supercoreLineState = 'hold';
            this._supercoreLineDuration = 90 + Math.random() * 60; // 1.5-2.5s
        }
        this._drawSupercoreLines();
    } else if (this._supercoreLineState === 'hold') {
        this._supercoreLineDuration--;
        if (this._supercoreLineDuration <= 0) {
            this._supercoreLineState = 'fadeout';
            this._supercoreLineDuration = 60; // fade out 1s
        }
        this._drawSupercoreLines();
    } else if (this._supercoreLineState === 'fadeout') {
        this._supercoreLineAlpha -= 1 / 60;
        if (this._supercoreLineAlpha <= 0) {
            this._supercoreLineAlpha = 0;
            this._supercoreLineState = 'idle';
            this._supercoreLineNextTime = 180 + Math.random() * 180;
            this._supercoreLinePairs = [];
            this._supercoreLineGraphics.clear();
        } else {
            this._drawSupercoreLines();
        }
    }
};

Scene_SkillTree.prototype._pickRandomSupercorePairs = function () {
    const pairs = [];
    const n = this._superCores.length;
    const used = new Set();
    const pairCount = 1 + Math.floor(Math.random() * Math.min(3, (n * (n - 1)) / 2));
    for (let i = 0; i < pairCount; i++) {
        let a,
            b,
            tries = 0;
        do {
            a = Math.floor(Math.random() * n);
            b = Math.floor(Math.random() * n);
            tries++;
        } while ((a === b || used.has(a + ',' + b) || used.has(b + ',' + a)) && tries < 20);
        if (a !== b && !used.has(a + ',' + b) && !used.has(b + ',' + a)) {
            pairs.push([a, b]);
            used.add(a + ',' + b);
        }
    }
    return pairs;
};

Scene_SkillTree.prototype._drawSupercoreLines = function () {
    const g = this._supercoreLineGraphics;
    g.clear();
    const t = performance.now() / 1000;
    const alphaFade = this._supercoreLineAlpha || 0;
    const linePulse = 0.7 + 0.3 * Math.sin(t * 1.2 + 2.1);
    const mainAlpha = (0.13 + 0.11 * linePulse) * alphaFade;
    const glowAlpha = (0.05 + 0.04 * linePulse) * alphaFade;
    // Color: bluish-white
    const color = 0x99ccff;
    for (const [ai, bi] of this._supercoreLinePairs) {
        const a = this._superCores[ai];
        const b = this._superCores[bi];
        if (!a || !b) continue;
        // Glow
        g.lineStyle(6, color, glowAlpha, 0.5);
        g.moveTo(a.x, a.y);
        g.lineTo(b.x, b.y);
        // Main
        g.lineStyle(2, color, mainAlpha, 0.5);
        g.moveTo(a.x, a.y);
        g.lineTo(b.x, b.y);
    }
};

// --- Update method: call updateSupercoreLines ---
const _Scene_SkillTree_update = Scene_SkillTree.prototype.update;
Scene_SkillTree.prototype.update = function () {
    _Scene_SkillTree_update.call(this);
    if (this.updateSupercoreLines) this.updateSupercoreLines();
};

// --- Sun Constellation State ---
Scene_SkillTree.prototype.createSunConstellationManager = function () {
    this._sunConstellationGraphics = new PIXI.Graphics();
    this._sunConstellationAlpha = 0;
    this._sunConstellationState = 'idle';
    this._sunConstellationTimer = 0;
    this._sunConstellationDuration = 0;
    this._sunConstellationLines = [];
    this._sunConstellationNextTime = this._randomConstellationDelay();
    this.addChild(this._sunConstellationGraphics);
};

Scene_SkillTree.prototype.updateSunConstellations = function () {
    if (!this._superCores || this._superCores.length < 2) return;
    if (this._sunConstellationState === 'idle') {
        this._sunConstellationTimer++;
        if (this._sunConstellationTimer >= this._sunConstellationNextTime) {
            this._sunConstellationTimer = 0;
            this._sunConstellationState = 'fadein';
            this._sunConstellationAlpha = 0;
            this._sunConstellationDuration = 80;
            this._sunConstellationLines = this._generateSunConstellationLines();
        }
    } else if (this._sunConstellationState === 'fadein') {
        this._sunConstellationAlpha += 1 / 80;
        if (this._sunConstellationAlpha >= 0.7) {
            this._sunConstellationAlpha = 0.7;
            this._sunConstellationState = 'hold';
            this._sunConstellationDuration = 120 + Math.random() * 60;
        }
        this._drawSunConstellationLines();
    } else if (this._sunConstellationState === 'hold') {
        this._sunConstellationDuration--;
        if (this._sunConstellationDuration <= 0) {
            this._sunConstellationState = 'fadeout';
            this._sunConstellationDuration = 80;
        }
        this._drawSunConstellationLines();
    } else if (this._sunConstellationState === 'fadeout') {
        this._sunConstellationAlpha -= 1 / 80;
        if (this._sunConstellationAlpha <= 0) {
            this._sunConstellationAlpha = 0;
            this._sunConstellationState = 'idle';
            this._sunConstellationNextTime = this._randomConstellationDelay();
            this._sunConstellationLines = [];
            this._sunConstellationGraphics.clear();
        } else {
            this._drawSunConstellationLines();
        }
    }
};

Scene_SkillTree.prototype._generateSunConstellationLines = function () {
    // Use the same logic as _generateConstellationLines, but with this._superCores
    const stars = this._superCores;
    if (!stars || stars.length < 2) return [];
    const totalCount = Math.min(5 + Math.floor(Math.random() * 4), stars.length); // 5-8 points, or all suns if fewer
    const used = [];
    // Center of the screen
    const centerX = Graphics.width / 2;
    const centerY = Graphics.height / 2;
    // 1. Start with a random sun from the closest 20% to center
    const centerDistances = stars.map((s, i) => ({
        i,
        dist: Math.pow(s.x - centerX, 2) + Math.pow(s.y - centerY, 2),
    }));
    centerDistances.sort((a, b) => a.dist - b.dist);
    const topN = Math.max(1, Math.floor(centerDistances.length * 0.2));
    let idx = centerDistances[Math.floor(Math.random() * topN)].i;
    used.push(idx);
    let prevIdx = idx;
    let prevAngle = null;
    // 2. Build the chain/arc backbone
    for (let step = 1; step < totalCount; step++) {
        let bestScore = -Infinity,
            bestIdx = -1;
        for (let i = 0; i < stars.length; i++) {
            if (used.includes(i)) continue;
            // Distance constraint
            const dx = stars[prevIdx].x - stars[i].x;
            const dy = stars[prevIdx].y - stars[i].y;
            const dist = Math.sqrt(dx * dx + dy * dy);
            if (dist < 180) continue; // Too close, skip
            // Angle constraint
            let angleScore = 0;
            if (prevAngle !== null) {
                const angle = Math.atan2(
                    stars[i].y - stars[prevIdx].y,
                    stars[i].x - stars[prevIdx].x
                );
                let dAngle = Math.abs(angle - prevAngle);
                dAngle = Math.min(dAngle, Math.abs(Math.PI * 2 - dAngle));
                if (dAngle < Math.PI / 8) angleScore -= 2;
                else if (dAngle > Math.PI * 0.7) angleScore -= 2;
                else angleScore += 1.5 - Math.abs(dAngle - Math.PI / 3);
            }
            let distScore = -Math.abs(dist - 90) / 40;
            const centerDist = Math.sqrt(
                Math.pow(stars[i].x - centerX, 2) + Math.pow(stars[i].y - centerY, 2)
            );
            let centerScore = -centerDist / 120;
            let score = angleScore + distScore + centerScore + Math.random() * 0.5;
            if (score > bestScore) {
                bestScore = score;
                bestIdx = i;
            }
        }
        if (bestIdx >= 0) {
            used.push(bestIdx);
            prevAngle = Math.atan2(
                stars[bestIdx].y - stars[prevIdx].y,
                stars[bestIdx].x - stars[prevIdx].x
            );
            prevIdx = bestIdx;
        } else {
            break;
        }
    }
    // 3. Build lines for backbone
    let lines = [];
    for (let i = 0; i < used.length - 1; i++) {
        const a = stars[used[i]];
        const b = stars[used[i + 1]];
        lines.push([a.x, a.y, b.x, b.y]);
    }
    // 4. Add 2-4 long branches from random backbone points to far unused stars
    const branchCount = Math.min(2 + Math.floor(Math.random() * 3), stars.length - used.length); // 2-4 branches
    for (let b = 0; b < branchCount; b++) {
        if (used.length < 3) break;
        const branchFrom = 1 + Math.floor(Math.random() * (used.length - 2));
        const unused = [];
        for (let i = 0; i < stars.length; i++) {
            if (used.includes(i)) continue;
            const dx = stars[used[branchFrom]].x - stars[i].x;
            const dy = stars[used[branchFrom]].y - stars[i].y;
            const dist = dx * dx + dy * dy;
            unused.push({ i, dist });
        }
        if (unused.length > 0) {
            unused.sort((a, b) => b.dist - a.dist);
            const topN = Math.max(1, Math.floor(unused.length * 0.2));
            const pick = unused[Math.floor(Math.random() * topN)];
            const a = stars[used[branchFrom]];
            const bStar = stars[pick.i];
            lines.push([a.x, a.y, bStar.x, bStar.y]);
            used.push(pick.i);
        }
    }
    if (Math.random() < 0.15 && used.length > 3) {
        const a = stars[used[0]];
        const b = stars[used[used.length - 1]];
        lines.push([a.x, a.y, b.x, b.y]);
    }
    return lines;
};

Scene_SkillTree.prototype._drawSunConstellationLines = function () {
    const g = this._sunConstellationGraphics;
    g.clear();
    const t = performance.now() / 1000;
    const alphaFade = this._sunConstellationAlpha || 0;
    const linePulse = 0.7 + 0.3 * Math.sin(t * 1.2 + 1.5);
    const mainAlpha = (0.18 + 0.14 * linePulse) * alphaFade;
    const glowAlpha = (0.06 + 0.04 * linePulse) * alphaFade;
    g.lineStyle(2, 0x99ccff, mainAlpha, 0.5);
    for (const line of this._sunConstellationLines) {
        g.moveTo(line[0], line[1]);
        g.lineTo(line[2], line[3]);
    }
    g.lineStyle(4, 0x99ccff, glowAlpha, 0.5);
    for (const line of this._sunConstellationLines) {
        g.moveTo(line[0], line[1]);
        g.lineTo(line[2], line[3]);
    }
};

// --- Alternating logic in update ---
const _Scene_SkillTree_updateConstellations = Scene_SkillTree.prototype.updateConstellations;
Scene_SkillTree.prototype.updateConstellations = function () {
    // Update both constellations' state machines every frame
    _Scene_SkillTree_updateConstellations.call(this);
    this.updateSunConstellations();

    // Use a shared alternation flag based on a timer
    if (!this._constellationAlternator) {
        this._constellationAlternator = 0;
        this._constellationAlternatorTimer = 0;
        this._constellationAlternatorDuration = 0;
    }
    this._constellationAlternatorTimer++;
    // Alternate every time a constellation finishes its fadeout
    if (this._constellationState === 'idle' && this._sunConstellationState !== 'idle') {
        this._constellationAlternator = 1; // Show sun constellation
        this._constellationAlternatorDuration = this._sunConstellationDuration;
    } else if (this._sunConstellationState === 'idle' && this._constellationState !== 'idle') {
        this._constellationAlternator = 0; // Show regular constellation
        this._constellationAlternatorDuration = this._constellationDuration;
    }
    // Set visibility
    if (this._constellationAlternator === 1) {
        if (this._constellationGraphics) this._constellationGraphics.visible = false;
        if (this._sunConstellationGraphics) this._sunConstellationGraphics.visible = true;
    } else {
        if (this._constellationGraphics) this._constellationGraphics.visible = true;
        if (this._sunConstellationGraphics) this._sunConstellationGraphics.visible = false;
    }
};

// --- Call createSunConstellationManager in create() ---
const _Scene_SkillTree_create = Scene_SkillTree.prototype.create;
Scene_SkillTree.prototype.create = function () {
    _Scene_SkillTree_create.call(this);
    this.createSunConstellationManager();
};

// Helper to convert color string to hex number
function colorToHexNum(color) {
    if (typeof color === 'number') return color;
    if (typeof color === 'string') {
        if (color.startsWith('#')) {
            return parseInt(color.replace('#', '0x'));
        } else if (color.startsWith('0x')) {
            return parseInt(color);
        }
    }
    // Fallback to gold
    return 0xffe066;
}

//=============================================================================
// Shooting Star Sprite
//=============================================================================
class Sprite_ShootingStar extends Sprite {
    constructor() {
        super();

        // Random starting position (off-screen)
        const side = Math.floor(Math.random() * 4); // 0=top, 1=right, 2=bottom, 3=left
        switch (side) {
            case 0: // Top
                this.x = Math.random() * Graphics.width;
                this.y = -50;
                this._vx = (Math.random() - 0.5) * 4;
                this._vy = 2 + Math.random() * 4;
                break;
            case 1: // Right
                this.x = Graphics.width + 50;
                this.y = Math.random() * Graphics.height;
                this._vx = -2 - Math.random() * 4;
                this._vy = (Math.random() - 0.5) * 4;
                break;
            case 2: // Bottom
                this.x = Math.random() * Graphics.width;
                this.y = Graphics.height + 50;
                this._vx = (Math.random() - 0.5) * 4;
                this._vy = -2 - Math.random() * 4;
                break;
            case 3: // Left
                this.x = -50;
                this.y = Math.random() * Graphics.height;
                this._vx = 2 + Math.random() * 4;
                this._vy = (Math.random() - 0.5) * 4;
                break;
        }

        // Create trail graphics
        this._trailGraphics = new PIXI.Graphics();
        this.addChild(this._trailGraphics);

        // Trail properties
        this._trailPoints = [];
        this._maxTrailLength = 15 + Math.random() * 10; // 15-25 points
        this._lifetime = 0;
        this._maxLifetime = 120 + Math.random() * 60; // 2-3 seconds
        this._isFinished = false;

        // Color
        const colors = [0xffffff, 0xffff99, 0x99ccff, 0xffcc99];
        this._color = colors[Math.floor(Math.random() * colors.length)];
    }

    update() {
        super.update();

        if (this._isFinished) return;

        this._lifetime++;

        // Move
        this.x += this._vx;
        this.y += this._vy;

        // Add current position to trail
        this._trailPoints.push({ x: this.x, y: this.y });
        if (this._trailPoints.length > this._maxTrailLength) {
            this._trailPoints.shift();
        }

        // Calculate fade-out effect based on lifetime
        const fadeStartRatio = 0.7; // Start fading when 70% of lifetime is reached
        const lifetimeRatio = this._lifetime / this._maxLifetime;
        let fadeMultiplier = 1.0;

        if (lifetimeRatio > fadeStartRatio) {
            // Fade from 1.0 to 0.0 over the last 30% of lifetime
            const fadeProgress = (lifetimeRatio - fadeStartRatio) / (1.0 - fadeStartRatio);
            fadeMultiplier = 1.0 - fadeProgress;
        }

        // Draw trail with fade effect
        this._trailGraphics.clear();
        if (this._trailPoints.length > 1) {
            for (let i = 1; i < this._trailPoints.length; i++) {
                const baseAlpha = (i / this._trailPoints.length) * 0.8;
                const alpha = baseAlpha * fadeMultiplier; // Apply fade effect
                const width = (i / this._trailPoints.length) * 3 + 1;

                this._trailGraphics.lineStyle(width, this._color, alpha);
                this._trailGraphics.moveTo(
                    this._trailPoints[i - 1].x - this.x,
                    this._trailPoints[i - 1].y - this.y
                );
                this._trailGraphics.lineTo(
                    this._trailPoints[i].x - this.x,
                    this._trailPoints[i].y - this.y
                );
            }
        }

        // Check if completely faded out or way off-screen
        if (
            this._lifetime > this._maxLifetime ||
            this.x < -200 ||
            this.x > Graphics.width + 200 ||
            this.y < -200 ||
            this.y > Graphics.height + 200
        ) {
            this._isFinished = true;
        }
    }
}

//=============================================================================
// Solar Flare Sprite
//=============================================================================
class Sprite_SolarFlare extends Sprite {
    constructor(x, y, coreSize, coreColor) {
        super();

        this.x = x;
        this.y = y;
        this._coreSize = coreSize;
        this._coreColor = coreColor;

        // Flare properties
        this._flareGraphics = new PIXI.Graphics();
        this.addChild(this._flareGraphics);

        this._lifetime = 0;
        this._maxLifetime = 90 + Math.random() * 60; // 1.5-2.5 seconds
        this._isFinished = false;

        // Expanding ring properties
        this._rings = [];
        const ringCount = 2 + Math.floor(Math.random() * 3); // 2-4 rings

        for (let i = 0; i < ringCount; i++) {
            this._rings.push({
                radius: 0,
                maxRadius: coreSize * (2 + i * 1.5 + Math.random() * 2),
                speed: 0.8 + Math.random() * 1.2,
                alpha: 0.6 - i * 0.15,
                width: 3 + Math.random() * 2,
            });
        }
    }

    update() {
        super.update();

        if (this._isFinished) return;

        this._lifetime++;

        // Update rings
        for (const ring of this._rings) {
            ring.radius += ring.speed;

            if (ring.radius > ring.maxRadius) {
                ring.radius = ring.maxRadius;
            }
        }

        // Draw expanding rings
        this._flareGraphics.clear();

        for (const ring of this._rings) {
            if (ring.radius > 0) {
                const fadeProgress = this._lifetime / this._maxLifetime;
                const alpha = ring.alpha * (1 - fadeProgress);

                if (alpha > 0) {
                    // Outer glow
                    this._flareGraphics.lineStyle(ring.width + 2, 0xffffff, alpha * 0.3);
                    this._flareGraphics.drawCircle(0, 0, ring.radius);

                    // Main ring
                    const color =
                        typeof this._coreColor === 'string' && this._coreColor.startsWith('#')
                            ? parseInt(this._coreColor.replace('#', '0x'))
                            : 0xffe066;
                    this._flareGraphics.lineStyle(ring.width, color, alpha);
                    this._flareGraphics.drawCircle(0, 0, ring.radius);
                }
            }
        }

        // Finish when lifetime expires
        if (this._lifetime >= this._maxLifetime) {
            this._isFinished = true;
        }
    }
}

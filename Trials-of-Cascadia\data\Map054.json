{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Wood2", "battleback2Name": "Smoke", "bgm": {"name": "RNR2_Destiny_noPerc_noMel_Loop", "pan": 0, "pitch": 100, "volume": 30}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 20, "note": "<Fog 1 Settings>\n Name: !leavessmall\n Opacity: 150\n</Fog 1 Settings>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 8, "width": 17, "data": [5984, 5988, 6012, 6012, 6012, 6012, 6012, 5992, 5984, 5988, 6012, 6012, 6012, 6012, 6012, 5992, 5984, 5988, 6022, 6370, 6370, 6370, 6370, 6370, 6024, 5996, 6022, 6370, 6370, 6370, 6370, 6370, 6024, 5992, 6008, 6374, 6376, 6376, 6376, 6376, 6376, 6375, 6028, 6375, 6376, 6376, 6376, 6376, 6376, 6371, 6000, 6008, 6380, 2850, 2836, 2836, 2836, 2852, 6377, 6375, 6380, 2850, 2836, 2836, 2836, 2852, 6377, 6000, 6008, 2850, 2817, 2816, 2816, 2816, 2818, 2852, 6381, 2850, 2817, 2816, 2816, 2816, 2818, 2852, 6000, 6008, 2832, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2840, 6000, 6008, 2856, 2824, 2816, 2816, 2816, 2820, 2854, 6026, 2856, 2824, 2816, 2816, 2816, 2820, 2854, 6000, 5986, 6020, 2832, 2816, 2816, 2816, 2840, 6018, 5987, 6020, 2832, 2816, 2820, 2844, 2854, 6018, 5985, 5984, 6008, 2834, 2844, 2844, 2824, 2840, 6000, 5984, 6008, 2832, 2816, 2840, 6027, 6017, 5993, 5984, 5988, 6022, 2848, 6027, 6021, 2832, 2840, 6024, 6012, 6022, 2832, 2816, 2840, 6371, 6370, 6024, 5992, 6008, 6374, 2848, 6371, 6028, 2832, 2840, 6371, 6370, 6374, 2832, 2816, 2840, 6377, 6376, 6371, 6000, 6008, 6380, 2848, 6377, 6375, 2832, 2840, 6377, 6376, 6380, 2832, 2816, 2818, 2836, 2852, 6377, 6000, 6008, 2850, 2819, 2852, 6381, 2832, 2818, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2818, 2852, 6000, 6008, 2832, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 6000, 6008, 2856, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 6000, 5986, 6004, 6004, 6004, 6020, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 6008, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 6000, 5984, 5984, 5984, 5984, 5986, 6020, 2856, 2844, 2828, 2844, 2844, 2844, 2844, 2844, 2854, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6020, 2860, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3810, 3812, 0, 0, 0, 0, 0, 0, 3810, 3812, 0, 0, 0, 0, 0, 0, 0, 3816, 3786, 3812, 0, 0, 0, 3810, 3797, 3805, 3806, 3821, 0, 0, 0, 0, 0, 0, 0, 3816, 3786, 3796, 3796, 3796, 3777, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 32, 0, 32, 304, 0, 0, 0, 304, 32, 0, 32, 304, 0, 0, 0, 0, 312, 40, 0, 40, 312, 0, 0, 0, 312, 40, 0, 40, 312, 0, 0, 0, 0, 320, 1, 0, 1, 320, 0, 0, 0, 320, 1, 0, 1, 320, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 296, 0, 0, 296, 0, 0, 0, 0, 296, 0, 296, 296, 0, 0, 0, 0, 296, 0, 0, 296, 296, 0, 0, 0, 296, 296, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 297, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Object06", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 2, "y": 2}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Object06", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 6, "y": 2}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Object06", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 10, "y": 2}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Object06", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 250, "indent": 1, "parameters": [{"name": "Coin", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 14, "y": 2}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"2\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, null, null, null, null, null, null, null, null, null, null, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 57, 39, 30, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 18}]}
{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "SmokeForest", "battleback2Name": "SmokeForest", "bgm": {"name": "DarkFantasyForestLoop2", "pan": 0, "pitch": 100, "volume": 70}, "bgs": {"name": "Multiple-Whispering-Voices", "pan": 0, "pitch": 100, "volume": 50}, "disableDashing": false, "displayName": "Smoke Forest", "encounterList": [], "encounterStep": 30, "height": 60, "note": "<Zoom: 200%>\n<Color Adjust Saturate: -0.25>\n<Fog 1 Settings>\n Name: !wispy_white_clouds\n Opacity: 100\n Horz Scroll: 1.0\n</Fog 1 Settings>\n<Bloom Threshold: 0.3>\n<Bloom Scale: 0.3>\n<Bloom Brightness: 1.5>\n", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 19, "width": 60, "data": [2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2480, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2480, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2480, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2480, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2432, 2436, 2470, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2440, 2432, 2456, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2448, 2432, 2434, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2440, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2846, 2853, 2448, 2432, 2456, 2834, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3714, 3701, 3713, 3713, 3713, 3713, 3713, 3713, 3702, 3700, 3701, 3725, 2848, 2448, 2432, 2456, 2848, 3723, 3702, 3700, 3700, 3716, 2856, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3714, 3685, 3718, 2850, 2836, 2836, 2836, 2836, 2852, 3696, 3684, 3718, 2851, 2855, 2448, 2432, 2456, 2833, 2852, 3720, 3708, 3708, 3710, 3713, 3702, 3700, 3716, 2856, 2844, 2844, 2844, 2844, 2824, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3696, 3704, 2850, 2817, 2816, 2816, 2816, 2820, 2854, 3698, 3718, 2850, 2842, 2466, 2433, 2436, 2470, 2832, 2818, 2836, 2836, 2836, 2836, 2852, 3720, 3708, 3710, 3713, 3702, 3700, 3700, 3716, 2856, 2854, 3714, 3700, 3701, 3713, 3713, 3713, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 3714, 3681, 3704, 2856, 2824, 2816, 2816, 2816, 2840, 3723, 3719, 2850, 2821, 2854, 2448, 2432, 2456, 3722, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2852, 3720, 3708, 3708, 3710, 3713, 3713, 3709, 3708, 3718, 2850, 2836, 2836, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2840, 3714, 3701, 3709, 3708, 3710, 3717, 2856, 2844, 2844, 2844, 2826, 2836, 2836, 2821, 2854, 2466, 2433, 2436, 2470, 3712, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3702, 3701, 3725, 2832, 2816, 2816, 2840, 3698, 3718, 2850, 2836, 2852, 3697, 3701, 3713, 3713, 3725, 2832, 2820, 2844, 2854, 2466, 2433, 2436, 2470, 3714, 3683, 3716, 2856, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3720, 3718, 2850, 2817, 2820, 2844, 2854, 3712, 2850, 2817, 2816, 2840, 3720, 3718, 2850, 2836, 2836, 2821, 2854, 2466, 2452, 2433, 2436, 2470, 3723, 3709, 3708, 3710, 3713, 3713, 3725, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2840, 3723, 3703, 3719, 2832, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2840, 2466, 2433, 2432, 2436, 2470, 2850, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3712, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2436, 2470, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2481, 2480, 2482, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 4451, 4454, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2466, 2452, 2433, 2432, 2434, 2468, 2832, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2854, 4449, 4452, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3722, 2856, 2824, 2816, 2816, 2816, 2816, 2820, 2854, 2466, 2433, 2432, 2432, 2432, 2436, 2470, 2832, 2816, 2816, 2816, 2840, 4787, 4786, 4786, 4790, 4457, 4460, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3697, 3716, 2856, 2824, 2816, 2820, 2844, 2854, 2466, 2433, 2436, 2460, 2460, 2460, 2470, 2850, 2817, 2816, 2816, 2816, 2840, 4785, 4784, 4784, 4788, 4787, 4790, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3715, 3709, 3710, 3725, 2834, 2844, 2854, 2466, 2452, 2433, 2436, 2470, 2850, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2840, 4793, 4792, 4792, 4796, 4793, 4796, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 3715, 3719, 2850, 2836, 2837, 2855, 2466, 2452, 2437, 2460, 2460, 2470, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2837, 2849, 2838, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3702, 3701, 3719, 2850, 2817, 2820, 2854, 2466, 2437, 2460, 2470, 2850, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2816, 2816, 2816, 2840, 3722, 2856, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3696, 3704, 2850, 2817, 2816, 2840, 2466, 2433, 2456, 2851, 2849, 2845, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2856, 2824, 2816, 2820, 2854, 3697, 3700, 3701, 3713, 3713, 3713, 3713, 3717, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3698, 3718, 2832, 2816, 2816, 2840, 2448, 2436, 2470, 2848, 3714, 3716, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 3722, 2856, 2844, 2854, 3714, 3681, 3684, 3718, 2850, 2836, 2836, 2852, 3712, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3707, 2859, 2825, 2816, 2816, 2840, 2448, 2456, 2859, 2843, 3696, 3682, 3716, 2856, 2844, 2824, 2816, 2816, 2820, 2844, 2854, 3714, 3683, 3700, 3700, 3701, 3709, 3688, 3704, 2859, 2845, 2824, 2816, 2840, 3697, 3700, 3716, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3699, 3725, 2832, 2816, 2816, 2840, 2448, 2434, 2468, 2848, 3720, 3688, 3686, 3713, 3725, 2832, 2816, 2816, 2840, 3723, 3713, 3709, 3708, 3688, 3684, 3718, 2858, 3720, 3710, 3702, 3716, 2856, 2844, 2854, 3698, 3708, 3690, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3712, 2850, 2817, 2816, 2816, 2840, 2448, 2432, 2456, 2833, 2852, 3720, 3718, 2850, 2836, 2817, 2816, 2816, 2818, 2836, 2836, 2836, 2852, 3720, 3718, 2850, 2819, 2836, 2852, 3720, 3710, 3713, 3713, 3713, 3719, 2858, 3696, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3712, 2832, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2818, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2842, 3698, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2840, 2481, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3712, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2840, 2481, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2832, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2846, 2849, 2845, 2844, 2824, 2816, 2840, 2448, 2432, 2456, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3702, 3701, 3725, 2832, 2816, 2840, 2472, 2440, 2434, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3696, 3704, 2850, 2817, 2816, 2818, 2852, 2472, 2440, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3714, 3716, 2856, 2824, 2816, 2816, 2816, 2820, 2854, 3722, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3698, 3718, 2832, 2816, 2816, 2816, 2818, 2852, 2448, 2456, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3714, 3681, 3682, 3716, 2856, 2824, 2816, 2816, 2840, 3723, 3711, 3717, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3712, 2850, 2817, 2816, 2816, 2816, 2816, 2840, 2448, 2434, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3709, 3708, 3688, 3682, 3716, 2856, 2844, 2824, 2818, 2837, 2861, 3712, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3707, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2440, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2846, 2849, 2849, 2853, 3696, 3680, 3682, 3700, 3716, 2856, 2828, 2854, 3714, 3706, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3712, 2856, 2824, 2816, 2816, 2816, 2816, 2818, 2852, 2448, 2456, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3713, 3717, 2860, 3696, 3680, 3680, 3684, 3710, 3725, 2848, 3714, 3685, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 3697, 3716, 2856, 2824, 2816, 2816, 2816, 2816, 2840, 2448, 2434, 2468, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2846, 2839, 2861, 3697, 3700, 3681, 3680, 3684, 3718, 2850, 2837, 2855, 3696, 3704, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3713, 3702, 3681, 3682, 3716, 2856, 2844, 2824, 2816, 2816, 2840, 2472, 2440, 2434, 2468, 2856, 2824, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2854, 3726, 2848, 3715, 3709, 3708, 3708, 3708, 3718, 2850, 2821, 2854, 3714, 3685, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 3720, 3708, 3688, 3682, 3700, 3716, 2856, 2844, 2824, 2818, 2852, 2472, 2440, 2434, 2468, 2832, 2816, 2820, 2844, 2854, 3723, 3713, 3713, 3725, 2859, 2855, 3724, 2850, 2837, 2849, 2849, 2849, 2845, 2854, 3715, 3709, 3718, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 3720, 3688, 3680, 3682, 3700, 3716, 2856, 2844, 2846, 2853, 2448, 2432, 2456, 2834, 2844, 2854, 3715, 3725, 2851, 2849, 2838, 2852, 3714, 3716, 2859, 2845, 2854, 3714, 3700, 3700, 3700, 3701, 3719, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3720, 3708, 3708, 3708, 3710, 3713, 3713, 3725, 2848, 2448, 2432, 2456, 2848, 3723, 3713, 3719, 2850, 2842, 3726, 2856, 2854, 3720, 3710, 3713, 3702, 3700, 3685, 3708, 3708, 3708, 3718, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2842, 2448, 2432, 2456, 2833, 2836, 2836, 2836, 2817, 2818, 2852, 3723, 3725, 2850, 2836, 2852, 3696, 3680, 3704, 2850, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2840, 3696, 3684, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3696, 3704, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3720, 3690, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 3696, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3714, 3681, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2481, 2480, 2482, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3696, 3680, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3806, 3813, 0, 0, 0, 3794, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 3808, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3810, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 3811, 3815, 0, 0, 0, 3793, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3784, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 3810, 3777, 3776, 3776, 3776, 3780, 3814, 0, 0, 3810, 3802, 0, 0, 0, 0, 3792, 3778, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 0, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3814, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3800, 0, 0, 3810, 3781, 3814, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3810, 3796, 3796, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3786, 3796, 3796, 3781, 3814, 0, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 3810, 3796, 3812, 0, 0, 0, 0, 0, 3792, 3780, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3777, 3780, 3804, 3814, 0, 3810, 3777, 3776, 3800, 0, 0, 3810, 3796, 3796, 3781, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3777, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3778, 3796, 3796, 3777, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3810, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3784, 3780, 3804, 3804, 3786, 3812, 0, 3810, 3781, 3804, 3804, 3804, 3784, 3780, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3814, 0, 0, 3816, 3814, 0, 0, 3816, 3801, 0, 3794, 3814, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3788, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3810, 3812, 0, 0, 3810, 3812, 0, 3795, 3809, 3803, 0, 3810, 3796, 3812, 0, 0, 3810, 3796, 3796, 3812, 0, 0, 0, 3810, 3812, 0, 0, 3816, 3804, 3784, 3776, 3776, 3780, 3804, 3814, 0, 0, 3820, 0, 0, 3816, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3777, 3778, 3796, 3796, 3777, 3778, 3796, 3802, 0, 3793, 3796, 3777, 3776, 3778, 3796, 3796, 3777, 3780, 3804, 3814, 0, 0, 0, 3816, 3786, 3796, 3812, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 3810, 3812, 0, 3810, 3812, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3782, 3809, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 3792, 3776, 3778, 3796, 3797, 3805, 3804, 3804, 3814, 0, 0, 3792, 3778, 3796, 3777, 3778, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 3816, 3784, 3776, 3780, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 3794, 3804, 3814, 0, 0, 0, 0, 0, 3810, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 3810, 3796, 3797, 3815, 0, 0, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3797, 3809, 3798, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3810, 3777, 3780, 3814, 0, 0, 0, 0, 3810, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3784, 3776, 3776, 3776, 3800, 0, 3816, 3804, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3777, 3776, 3800, 0, 0, 0, 3811, 3809, 3805, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3816, 3784, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 3808, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3782, 3821, 0, 3816, 3804, 3814, 0, 0, 0, 0, 3810, 3796, 3796, 3812, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3819, 3785, 3776, 3776, 3800, 0, 0, 3819, 3803, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3780, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3805, 3784, 3776, 3800, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 3808, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 3818, 0, 0, 0, 0, 3816, 3804, 3814, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3810, 3777, 3776, 3780, 3814, 0, 0, 0, 3817, 3813, 0, 0, 3810, 3796, 3777, 3776, 3776, 3778, 3796, 3796, 3796, 3812, 0, 0, 3810, 3779, 3796, 3812, 0, 0, 0, 0, 0, 0, 3818, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3801, 0, 3794, 3804, 3804, 3814, 0, 0, 0, 0, 0, 3817, 3809, 3809, 3805, 3784, 3780, 3804, 3804, 3784, 3780, 3804, 3804, 3806, 3798, 3796, 3777, 3780, 3804, 3806, 3809, 3798, 3796, 3796, 3796, 3796, 3802, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3814, 0, 0, 3808, 0, 3808, 0, 0, 0, 3818, 0, 0, 0, 3818, 0, 0, 0, 0, 3816, 3814, 0, 0, 3816, 3814, 0, 0, 0, 3816, 3804, 3804, 3814, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3801, 0, 3811, 3805, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3810, 3796, 3783, 3809, 3787, 3796, 3796, 3796, 3802, 0, 0, 0, 3793, 3796, 3796, 3796, 3812, 0, 0, 3810, 3812, 0, 0, 3810, 3796, 3812, 0, 0, 0, 0, 3810, 3796, 3812, 0, 0, 0, 0, 0, 3808, 0, 3808, 0, 3816, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3777, 3776, 3800, 0, 3792, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3778, 3796, 3796, 3777, 3778, 3796, 3796, 3777, 3776, 3778, 3796, 3796, 3796, 3796, 3777, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3783, 3809, 3787, 3812, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3806, 3809, 3805, 3804, 3784, 3776, 3800, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3792, 3778, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3782, 3809, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3777, 3776, 3778, 3812, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 3816, 3784, 3776, 3776, 3776, 3780, 3814, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3792, 3776, 3776, 3776, 3778, 3812, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3778, 3797, 3821, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3806, 3809, 3809, 3813, 0, 0, 0, 0, 0, 3816, 3788, 3814, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3820, 0, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3806, 3799, 3821, 0, 0, 0, 0, 0, 0, 3810, 3797, 3815, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3800, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3814, 0, 3808, 0, 0, 0, 0, 0, 0, 3810, 3781, 3814, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3812, 0, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3778, 3812, 0, 0, 0, 0, 3792, 3776, 3780, 3804, 3814, 0, 0, 0, 0, 3819, 3815, 0, 3810, 3797, 3809, 3809, 3809, 3805, 3814, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3812, 0, 0, 0, 0, 0, 0, 3816, 3804, 3806, 3813, 0, 0, 0, 3794, 3804, 3814, 0, 0, 3811, 3809, 3798, 3812, 0, 0, 3819, 3805, 3814, 0, 0, 0, 0, 0, 0, 3810, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 3808, 0, 0, 0, 3810, 3802, 0, 3816, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3802, 0, 0, 0, 3793, 3796, 3796, 3796, 3777, 3778, 3812, 0, 0, 3810, 3796, 3812, 0, 0, 0, 3810, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3777, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3814, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3800, 0, 0, 0, 3792, 3776, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3782, 3821, 0, 0, 3792, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 3810, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3800, 0, 0, 0, 3792, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 237, 237, 245, 237, 237, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 237, 237, 245, 237, 237, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 237, 237, 245, 237, 237, 245, 245, 0, 245, 245, 237, 237, 245, 237, 237, 0, 0, 0, 0, 0, 237, 237, 245, 237, 237, 245, 245, 0, 245, 245, 237, 237, 237, 237, 237, 237, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 237, 237, 245, 237, 237, 245, 245, 0, 245, 245, 0, 573, 0, 0, 0, 245, 245, 0, 245, 245, 0, 0, 0, 0, 0, 245, 245, 0, 245, 245, 0, 0, 0, 572, 0, 245, 245, 245, 245, 245, 245, 237, 237, 237, 237, 237, 237, 245, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 245, 245, 0, 245, 245, 0, 0, 0, 572, 0, 0, 564, 0, 0, 0, 572, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 572, 0, 245, 245, 245, 245, 245, 245, 0, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 252, 572, 573, 0, 0, 573, 0, 0, 0, 0, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 0, 0, 0, 573, 572, 573, 572, 0, 0, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 252, 564, 565, 564, 0, 0, 0, 565, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 0, 0, 0, 238, 252, 0, 572, 573, 572, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 0, 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 238, 0, 238, 252, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 238, 0, 252, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 226, 226, 226, 226, 238, 252, 572, 0, 0, 0, 572, 573, 0, 0, 565, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 557, 556, 557, 556, 0, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 226, 226, 226, 226, 252, 0, 565, 0, 0, 0, 0, 0, 0, 0, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 565, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 557, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 223, 557, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 0, 0, 0, 0, 0, 565, 0, 0, 0, 0, 0, 557, 556, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 223, 565, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 0, 0, 0, 0, 0, 573, 0, 0, 0, 0, 0, 565, 564, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 229, 229, 0, 0, 229, 229, 0, 0, 0, 0, 0, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 229, 229, 0, 0, 245, 245, 0, 0, 245, 245, 0, 0, 0, 0, 0, 245, 245, 245, 0, 229, 245, 0, 0, 0, 0, 0, 0, 0, 0, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 229, 229, 0, 229, 229, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 245, 245, 512, 245, 572, 573, 245, 0, 0, 573, 245, 0, 0, 0, 512, 0, 0, 722, 245, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 245, 0, 0, 0, 0, 0, 0, 0, 0, 245, 245, 0, 245, 245, 229, 229, 229, 229, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 573, 0, 564, 565, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 730, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 512, 245, 992, 993, 0, 0, 0, 0, 0, 0, 512, 0, 0, 245, 245, 245, 245, 512, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 676, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 565, 564, 512, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 556, 557, 0, 0, 0, 0, 0, 0, 0, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 560, 561, 562, 563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 557, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 238, 238, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 238, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 229, 229, 0, 0, 0, 229, 229, 0, 0, 0, 0, 0, 512, 0, 229, 229, 229, 0, 0, 229, 229, 0, 0, 229, 229, 0, 0, 0, 0, 0, 0, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 229, 229, 245, 245, 0, 0, 0, 245, 245, 245, 0, 0, 0, 0, 0, 0, 245, 245, 245, 0, 0, 245, 245, 229, 229, 245, 245, 245, 229, 229, 229, 229, 245, 245, 245, 0, 229, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 245, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 721, 722, 0, 0, 0, 0, 245, 245, 0, 0, 0, 245, 245, 245, 245, 0, 0, 0, 512, 245, 245, 245, 245, 0, 0, 0, 512, 229, 229, 229, 229, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 728, 729, 730, 0, 0, 0, 0, 0, 0, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 245, 245, 245, 512, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 238, 0, 0, 0, 0, 29, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 736, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 744, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 238, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 752, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 252, 0, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 760, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 556, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 565, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 556, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 236, 236, 236, 236, 0, 0, 0, 0, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 608, 557, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 556, 564, 556, 0, 0, 557, 556, 0, 0, 564, 556, 0, 0, 0, 0, 557, 556, 0, 0, 0, 0, 0, 557, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 557, 556, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 564, 572, 564, 0, 0, 0, 0, 0, 0, 572, 564, 0, 0, 0, 0, 565, 564, 0, 0, 0, 0, 0, 565, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 0, 0, 0, 512, 512, 512, 512, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 0, 512, 512, 512, 512, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 229, 229, 229, 229, 229, 0, 0, 0, 0, 0, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 229, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 556, 557, 237, 556, 557, 237, 237, 237, 237, 237, 0, 0, 0, 0, 0, 237, 237, 237, 237, 237, 556, 557, 237, 556, 557, 237, 237, 237, 237, 237, 237, 229, 229, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 237, 237, 237, 237, 237, 556, 557, 237, 556, 557, 564, 565, 245, 564, 565, 556, 557, 237, 556, 557, 0, 0, 0, 0, 0, 556, 557, 237, 556, 557, 564, 565, 245, 564, 565, 556, 557, 556, 557, 556, 557, 237, 237, 237, 237, 237, 237, 237, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 238, 556, 557, 237, 556, 557, 564, 565, 245, 564, 565, 572, 556, 557, 572, 573, 564, 565, 245, 564, 565, 0, 0, 318, 319, 0, 564, 565, 245, 564, 565, 572, 573, 556, 557, 573, 564, 565, 564, 565, 564, 565, 556, 557, 556, 557, 556, 557, 237, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 238, 238, 564, 565, 245, 564, 565, 572, 573, 556, 557, 573, 556, 557, 565, 0, 556, 557, 573, 556, 557, 573, 0, 0, 326, 327, 0, 572, 556, 557, 572, 573, 556, 557, 564, 565, 0, 572, 556, 557, 556, 557, 573, 564, 565, 564, 565, 564, 565, 245, 169, 169, 169, 169, 169, 169, 169, 227, 238, 238, 238, 556, 557, 556, 557, 572, 556, 557, 0, 564, 565, 0, 564, 565, 573, 0, 564, 565, 0, 564, 565, 0, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 564, 565, 572, 573, 556, 557, 564, 556, 557, 565, 0, 572, 556, 557, 556, 557, 573, 0, 169, 169, 169, 169, 169, 169, 227, 238, 238, 238, 556, 557, 556, 557, 565, 0, 564, 556, 557, 572, 573, 0, 572, 573, 512, 513, 514, 515, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 572, 573, 0, 0, 564, 565, 572, 564, 565, 573, 0, 0, 564, 565, 564, 565, 0, 0, 169, 169, 169, 169, 169, 227, 238, 238, 556, 557, 564, 565, 564, 565, 573, 556, 557, 564, 565, 0, 0, 0, 0, 0, 520, 521, 522, 523, 0, 0, 0, 0, 0, 409, 410, 410, 410, 411, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 572, 573, 0, 0, 0, 572, 573, 572, 573, 0, 0, 169, 169, 169, 169, 227, 238, 238, 238, 564, 565, 572, 573, 572, 573, 0, 564, 565, 556, 557, 0, 0, 512, 512, 512, 528, 529, 530, 531, 512, 512, 512, 512, 512, 417, 418, 418, 418, 419, 512, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 512, 513, 514, 515, 0, 0, 0, 0, 0, 169, 169, 169, 169, 223, 238, 556, 557, 572, 573, 556, 557, 0, 0, 0, 556, 557, 564, 565, 0, 512, 512, 0, 0, 536, 537, 538, 539, 0, 0, 0, 0, 0, 425, 426, 426, 426, 427, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 520, 521, 522, 523, 0, 0, 0, 0, 0, 169, 169, 169, 169, 223, 238, 564, 565, 0, 556, 557, 556, 557, 556, 557, 564, 565, 572, 573, 0, 512, 0, 0, 0, 544, 545, 546, 547, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 556, 557, 556, 557, 512, 512, 512, 0, 0, 528, 529, 530, 531, 512, 512, 512, 512, 512, 169, 169, 169, 169, 223, 556, 557, 573, 0, 564, 565, 564, 565, 564, 556, 557, 573, 0, 0, 512, 512, 0, 0, 0, 552, 553, 554, 555, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 564, 565, 556, 564, 565, 564, 565, 557, 0, 512, 512, 512, 536, 537, 538, 539, 512, 512, 0, 0, 0, 169, 169, 169, 169, 223, 564, 556, 557, 0, 572, 573, 572, 573, 572, 564, 565, 0, 0, 512, 0, 0, 0, 0, 0, 560, 561, 562, 563, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 572, 573, 564, 572, 573, 572, 573, 565, 0, 0, 0, 0, 544, 545, 546, 547, 0, 0, 0, 0, 0, 169, 169, 169, 169, 223, 572, 564, 565, 0, 0, 0, 0, 0, 0, 572, 573, 0, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 572, 573, 572, 573, 572, 573, 0, 0, 0, 0, 552, 553, 554, 555, 0, 0, 0, 0, 0, 169, 169, 169, 169, 223, 556, 572, 573, 556, 557, 0, 0, 556, 557, 0, 0, 0, 512, 0, 0, 556, 557, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 0, 0, 512, 512, 556, 557, 556, 557, 512, 0, 0, 556, 557, 0, 556, 557, 0, 0, 556, 560, 561, 562, 563, 0, 0, 0, 0, 0, 169, 169, 169, 169, 556, 564, 565, 557, 564, 565, 556, 557, 564, 565, 0, 512, 512, 512, 0, 0, 564, 565, 556, 557, 564, 565, 564, 565, 0, 0, 0, 0, 0, 0, 556, 557, 564, 556, 557, 565, 556, 557, 0, 564, 556, 557, 564, 565, 0, 556, 564, 565, 557, 0, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 564, 572, 573, 565, 572, 573, 564, 565, 572, 573, 0, 0, 512, 0, 0, 0, 572, 573, 564, 565, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 564, 565, 572, 564, 565, 573, 564, 565, 0, 572, 564, 565, 572, 573, 0, 564, 572, 573, 565, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 573, 572, 573, 0, 0, 572, 573, 0, 0, 0, 0, 512, 0, 0, 696, 697, 698, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 572, 573, 0, 572, 573, 0, 0, 572, 573, 0, 0, 0, 572, 573, 572, 573, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 556, 557, 0, 0, 556, 557, 0, 0, 0, 0, 0, 704, 705, 706, 0, 0, 229, 229, 229, 229, 0, 0, 0, 229, 229, 0, 0, 0, 0, 976, 977, 978, 979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 229, 229, 564, 565, 229, 229, 564, 565, 229, 0, 512, 0, 229, 712, 713, 714, 229, 556, 557, 245, 245, 245, 0, 0, 0, 245, 245, 556, 557, 0, 0, 984, 985, 986, 987, 0, 0, 556, 557, 0, 556, 557, 512, 513, 514, 515, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 245, 556, 557, 556, 557, 245, 572, 556, 557, 0, 0, 0, 245, 720, 721, 556, 557, 564, 565, 0, 318, 319, 0, 0, 0, 0, 0, 564, 565, 229, 229, 984, 985, 986, 987, 0, 0, 564, 565, 229, 564, 565, 520, 521, 522, 523, 229, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 556, 557, 512, 513, 514, 515, 0, 0, 564, 565, 0, 512, 0, 0, 728, 729, 564, 565, 572, 573, 0, 326, 327, 513, 514, 515, 0, 0, 572, 573, 245, 644, 645, 646, 994, 995, 0, 0, 572, 573, 245, 572, 573, 528, 529, 530, 531, 245, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 564, 565, 520, 521, 522, 523, 0, 0, 572, 573, 0, 512, 512, 0, 0, 0, 572, 573, 0, 0, 0, 0, 520, 521, 522, 523, 0, 0, 0, 463, 463, 652, 653, 654, 884, 0, 877, 877, 0, 477, 478, 462, 0, 536, 537, 538, 539, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 556, 528, 529, 530, 531, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 530, 531, 0, 0, 0, 0, 463, 660, 661, 662, 892, 0, 0, 0, 0, 0, 0, 0, 0, 544, 545, 546, 547, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 219, 0, 564, 536, 537, 538, 539, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 538, 539, 0, 0, 0, 0, 0, 668, 669, 670, 0, 0, 0, 0, 0, 0, 0, 0, 0, 552, 553, 554, 555, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 219, 572, 544, 545, 546, 547, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 544, 545, 546, 547, 556, 557, 0, 0, 0, 789, 790, 678, 512, 0, 0, 556, 557, 556, 557, 0, 0, 560, 561, 562, 563, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 219, 552, 553, 554, 555, 0, 512, 512, 0, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 552, 553, 554, 555, 564, 565, 512, 0, 0, 0, 0, 0, 512, 512, 512, 564, 556, 557, 565, 512, 0, 0, 0, 0, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 560, 561, 562, 563, 0, 512, 512, 0, 564, 565, 557, 0, 0, 0, 0, 512, 512, 0, 572, 573, 556, 557, 556, 557, 572, 573, 0, 512, 0, 0, 0, 512, 512, 512, 512, 572, 564, 565, 573, 512, 0, 0, 0, 0, 0, 0, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 556, 557, 512, 512, 0, 556, 572, 573, 565, 0, 0, 0, 0, 512, 512, 512, 0, 0, 564, 565, 564, 565, 0, 0, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 572, 573, 0, 512, 512, 512, 0, 0, 0, 0, 238, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 564, 565, 0, 512, 512, 564, 565, 572, 573, 0, 318, 319, 0, 512, 512, 512, 512, 512, 572, 573, 572, 573, 512, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 238, 238, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 572, 573, 0, 512, 0, 572, 573, 0, 0, 0, 326, 327, 0, 0, 512, 512, 0, 0, 0, 556, 557, 0, 0, 0, 556, 557, 512, 512, 0, 0, 556, 557, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 568, 569, 570, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 573, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 564, 565, 556, 557, 0, 564, 565, 556, 557, 556, 557, 564, 565, 0, 0, 512, 513, 514, 515, 0, 512, 0, 0, 0, 576, 577, 578, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 229, 0, 0, 0, 229, 0, 696, 697, 698, 564, 565, 572, 573, 564, 565, 0, 572, 573, 564, 565, 564, 565, 572, 573, 0, 0, 520, 521, 522, 523, 0, 512, 0, 0, 0, 584, 585, 586, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 556, 557, 0, 0, 0, 556, 557, 229, 245, 0, 0, 0, 245, 229, 704, 705, 706, 572, 573, 556, 557, 572, 573, 556, 557, 229, 572, 573, 572, 573, 229, 556, 557, 0, 528, 529, 530, 531, 0, 512, 0, 0, 0, 592, 593, 594, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 564, 565, 0, 512, 0, 564, 565, 556, 557, 0, 0, 0, 0, 245, 712, 713, 714, 229, 229, 564, 565, 556, 557, 564, 565, 556, 557, 556, 557, 556, 557, 564, 565, 229, 536, 537, 538, 539, 0, 0, 0, 229, 0, 600, 601, 602, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 572, 573, 0, 0, 0, 572, 573, 564, 565, 0, 0, 0, 0, 556, 557, 556, 557, 245, 245, 572, 573, 564, 565, 572, 573, 564, 565, 564, 565, 564, 565, 572, 573, 245, 544, 545, 546, 547, 0, 512, 0, 245, 556, 557, 556, 557, 229, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 3, 4, 5, 6, 512, 512, 512, 512, 0, 572, 573, 556, 557, 0, 0, 564, 565, 564, 565, 0, 0, 556, 557, 572, 573, 0, 556, 557, 573, 572, 573, 572, 573, 0, 0, 0, 552, 553, 554, 555, 0, 0, 0, 0, 564, 565, 564, 565, 245, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 11, 12, 13, 14, 15, 512, 512, 0, 0, 0, 0, 564, 565, 0, 0, 572, 573, 572, 573, 0, 0, 564, 565, 557, 0, 0, 564, 565, 0, 0, 0, 0, 512, 512, 0, 0, 560, 561, 562, 563, 0, 512, 0, 0, 572, 556, 557, 573, 220, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 238, 19, 20, 21, 22, 23, 512, 512, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 565, 0, 0, 572, 573, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 564, 565, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 223, 238, 238, 27, 28, 556, 557, 31, 512, 0, 556, 557, 0, 0, 0, 556, 557, 0, 0, 556, 557, 0, 0, 556, 557, 572, 573, 737, 738, 0, 0, 0, 0, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 223, 238, 252, 0, 0, 564, 565, 512, 512, 0, 564, 556, 557, 0, 0, 564, 565, 0, 0, 564, 565, 0, 0, 564, 565, 556, 557, 745, 746, 747, 748, 0, 0, 0, 0, 0, 512, 512, 512, 512, 512, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 223, 556, 557, 0, 0, 572, 573, 0, 512, 0, 572, 564, 565, 0, 0, 572, 573, 0, 0, 572, 573, 0, 0, 572, 573, 564, 565, 753, 754, 755, 756, 0, 512, 512, 512, 0, 512, 512, 512, 512, 512, 512, 0, 0, 512, 0, 0, 0, 0, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 564, 565, 557, 0, 0, 0, 0, 512, 512, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 761, 762, 763, 764, 0, 0, 0, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 512, 0, 0, 0, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 573, 565, 0, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 512, 512, 512, 512, 512, 512, 0, 0, 0, 0, 512, 512, 0, 0, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 564, 572, 573, 0, 0, 0, 512, 608, 609, 610, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 556, 557, 512, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 587, 588, 589, 590, 591, 0, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 573, 556, 557, 0, 0, 0, 616, 617, 618, 619, 620, 512, 512, 0, 0, 0, 409, 410, 410, 410, 411, 0, 0, 512, 512, 556, 557, 565, 557, 512, 512, 0, 0, 0, 512, 512, 512, 512, 512, 595, 596, 597, 598, 599, 0, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 556, 557, 564, 565, 557, 0, 0, 624, 625, 626, 627, 628, 512, 512, 512, 512, 512, 417, 418, 418, 418, 419, 512, 512, 512, 0, 564, 565, 573, 565, 512, 512, 512, 512, 512, 512, 512, 512, 512, 512, 603, 604, 605, 606, 607, 0, 0, 220, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 512, 564, 565, 572, 573, 565, 556, 557, 632, 633, 634, 635, 636, 0, 0, 0, 0, 0, 425, 426, 426, 426, 427, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 611, 612, 613, 614, 615, 0, 0, 586, 229, 229, 229, 229, 229, 229, 229, 236, 236, 236, 236, 556, 572, 573, 557, 572, 573, 564, 565, 0, 0, 556, 557, 0, 0, 0, 0, 556, 557, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 564, 565, 557, 565, 557, 556, 572, 573, 557, 556, 557, 565, 557, 556, 557, 556, 564, 565, 557, 0, 0, 0, 556, 564, 556, 557, 0, 556, 557, 0, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 556, 557, 0, 556, 564, 565, 557, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 572, 573, 565, 573, 565, 564, 565, 564, 565, 564, 565, 573, 565, 564, 565, 564, 572, 573, 565, 0, 0, 0, 564, 572, 564, 565, 0, 564, 565, 0, 0, 0, 0, 0, 512, 512, 512, 0, 0, 0, 0, 0, 564, 565, 0, 564, 572, 573, 565, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 512, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 0, 0, 0, 572, 573, 572, 573, 512, 572, 573, 512, 512, 0, 0, 0, 0, 512, 512, 0, 0, 0, 512, 512, 572, 573, 512, 572, 573, 572, 573, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 0, 0, 0, 229, 229, 229, 229, 229, 229, 229, 229, 229, 0, 0, 0, 512, 512, 512, 0, 0, 0, 229, 229, 229, 229, 229, 229, 229, 229, 229, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 0, 0, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 0, 0, 512, 512, 512, 0, 0, 0, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "Grave Middle", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Statue03n", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["<Center>Here lies <PERSON><br>"]}, {"code": 401, "indent": 0, "parameters": ["1179-1223"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 39, "y": 47}, {"id": 2, "name": "EV002", "note": "<Hitbox Right: 8>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 50, 35, 1, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 35, "y": 59}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 12, "y": 15}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["Wave 8/VisuMZ_2_VisualBattleEnv", "BackEnvironmentRemove", "Back Environment: Remove", {"list:arraynum": "[\"1\"]"}]}, {"code": 657, "indent": 0, "parameters": ["ID(s) = [\"1\"]"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewPlayerLockedLight", "SPAWN LIGHT: Create Light(s) on Player", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"200\",\"intensity:num\":\"0.05\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Black", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Black"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Water_Mist", "WATER: Mist", {"MainData": "", "powerTarget:eval": "3", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"15\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"30\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.2\\\",\\\"scaleRatioY:num\\\":\\\"0.3\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#888888\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 3"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "DARK: <PERSON>", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"180\\\",\\\"opacityVariance:num\\\":\\\"40\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"30\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"2\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"45\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"-3\\\",\\\"spinSpeedVariance:eval\\\":\\\"2\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeClouds", "DARK: Smoke Clouds", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"5\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"128\\\",\\\"opacityVariance:num\\\":\\\"24\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#00e1e1\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0.7\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"5\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["Wave 7/VisuMZ_4_VisualFogs", "FogAddChangeSettings", "Fog: Add/Change Settings", {"Required": "", "id:num": "3", "filename:str": "DarkSpace", "Optional:struct": "{\"Scrolling\":\"\",\"_fogZero:eval\":\"false\",\"_fogLoopX:eval\":\"false\",\"_fogSx:eval\":\"-3\",\"_fogLoopY:eval\":\"false\",\"_fogSy:eval\":\"+0\",\"Appearance\":\"\",\"opacity:eval\":\"255\",\"blendMode:eval\":\"2\",\"hue:eval\":\"0\",\"hueShift:eval\":\"+0\",\"colorTone:eval\":\"[0, 0, 0, 0]\",\"Location\":\"\",\"maskRegions:arraynum\":\"[]\",\"maskTerrainTags:arraynum\":\"[]\",\"maskBlur:eval\":\"10\",\"maskSpill:eval\":\"10\",\"Vignette\":\"\",\"vignette:str\":\"Border\",\"vignetteFilename:str\":\"\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Required = "]}, {"code": 657, "indent": 0, "parameters": ["ID = 3"]}, {"code": 657, "indent": 0, "parameters": ["Filename = DarkSpace"]}, {"code": 657, "indent": 0, "parameters": ["Optional Settings = {\"Scrolling\":\"\",\"_fogZero:eval\":\"false\"…"]}, {"code": 121, "indent": 0, "parameters": [65, 65, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 1}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 31}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 41}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 49, "y": 29}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 31}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark red>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 71, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 52, "y": 18}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "IDA20-20High20Intensity20Loop", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 231, "indent": 0, "parameters": [1, "evil eye", 0, 0, 130, 0, 100, 100, 0, 1]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 130, 0, 100, 100, 100, 1, 60, false, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Stop! Don't move!"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What's wrong?!"]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["It's the Eye of Darkness. That which looks back"]}, {"code": 401, "indent": 0, "parameters": ["from the abyss. It consumes all it sees."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["If we move while the Darkness is heavy, it will"]}, {"code": 401, "indent": 0, "parameters": ["sense us and set its minions upon us."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Wait for it to pass, and we can move again."]}, {"code": 401, "indent": 0, "parameters": ["Just watch your step."]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 130, 0, 100, 100, 0, 1, 60, false, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 12, 'A', true)"]}, {"code": 241, "indent": 0, "parameters": [{"name": "DarkFantasyForestLoop", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 4}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 125, 0, 100, 100, 0, 1, 60, false, 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 1, 3]}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 0]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\",\"4\"]", "UpperLower:str": "both", "Duration:eval": "60", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"3\",\"4\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Black", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Black"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 355, "indent": 1, "parameters": ["setSelfSwitchValue(57, 16, 'A', false)"]}, {"code": 230, "indent": 1, "parameters": [360]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 111, "indent": 1, "parameters": [1, 119, 1, 120, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "evil eye", 0, 0, 130, 0, 100, 100, 0, 1]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 130, 0, 100, 100, 100, 1, 60, false, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_BloodRain", "DARK: Blood Rain", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"36\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"5\\\",\\\"totalPerPower:num\\\":\\\"5\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#cc0000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"6\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"12\\\",\\\"angle:eval\\\":\\\"255\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"5\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"36\\…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Slow_Icons_Mid", "SLOW: Flying Icons ●", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"50\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"InQuart\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.21\\\",\\\"scaleVariance:num\\\":\\\"0.2\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"false\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[\\\\\\\"evil eye\\\\\\\"]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"frozen\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"1\\\",\\\"ySwaySpeed:eval\\\":\\\"0\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Red", "Duration:num": "60"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Red"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.5", "Duration:num": "360", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 360"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 16, 'A', true)"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.25", "Duration:num": "60", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.25"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 2}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Skeleton1 A", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 90>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Show FoV>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 117, "indent": 0, "parameters": [114]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}], "x": 35, "y": 33}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 13, "y": 15}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Skeleton1 A", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Alert>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Time: 10>"]}, {"code": 108, "indent": 0, "parameters": ["<<PERSON><PERSON>: 90>"]}, {"code": 108, "indent": 0, "parameters": ["<Alert Show FoV>"]}, {"code": 108, "indent": 0, "parameters": ["<Encounter Direction Lock>"]}, {"code": 108, "indent": 0, "parameters": ["<Follower Trigger>"]}, {"code": 117, "indent": 0, "parameters": [114]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}], "x": 20, "y": 17}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gamePlayer.isMoving()"]}, {"code": 250, "indent": 1, "parameters": [{"name": "EVFXForge10_09_DreadforgeAura", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 225, "indent": 1, "parameters": [5, 5, 30, false]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, -1350, -950, 375, 375, 255, 0, 45, true, 1]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 301, "indent": 1, "parameters": [0, 71, false, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 15, "pitch": 120, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 3}, {"id": 17, "name": "EV017", "note": "<Hitbox Down: 9>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 58, 1, 17, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 59, "y": 11}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 46, "y": 23}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\I[2048]\\\\V[124]G\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.8\",\"startScaleY:eval\":\"0.8\",\"endScaleX:eval\":\"0.8\",\"endScaleY:eval\":\"0.8\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\I[2048]\\\\V[124]G\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 1, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 1, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 1, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "MsgPopupEvent", "Event Popup: Event", {"EventId:eval": "0", "MessageText:json": "\"<center>\\\\LastGainObj\"", "MsgDuration:eval": "180", "PopupExtra:struct": "{\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fadeOutDuration:eval\":\"60\",\"Offset\":\"\",\"startOffsetX:eval\":\"+0\",\"startOffsetY:eval\":\"-48\",\"endOffsetX:eval\":\"+0\",\"endOffsetY:eval\":\"-96\",\"Scale\":\"\",\"startScaleX:eval\":\"0.7\",\"startScaleY:eval\":\"0.7\",\"endScaleX:eval\":\"0.7\",\"endScaleY:eval\":\"0.7\",\"Angle\":\"\",\"startAngle:eval\":\"+0\",\"endAngle:eval\":\"+0\",\"Misc\":\"\",\"Arc:eval\":\"+0\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Message Text = \"<center>\\\\LastGainObj\""]}, {"code": 657, "indent": 0, "parameters": ["Message Duration = 180"]}, {"code": 657, "indent": 0, "parameters": ["Popup Settings = {\"Fade\":\"\",\"fadeInDuration:eval\":\"8\",\"fade…"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 47, "y": 23}, {"id": 20, "name": "Grave Right", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Statue03n", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["<Center>Here lies <PERSON><br>"]}, {"code": 401, "indent": 0, "parameters": ["1182-1223"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 46}, {"id": 21, "name": "EV021", "note": "<Hitbox Down: 7><Hitbox Left: 1><Hitbox Right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ash_sheet_1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewFollowerLockedLight", "SPAWN LIGHT: Create Light(s) on Follower", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"50\",\"intensity:num\":\"0.50\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "Target": "", "FollowerIndex:eval": "1", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Target = "]}, {"code": 657, "indent": 0, "parameters": ["Follower Index = 1"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["You already know you will fail in your quest to prevent"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>'s ressurection. Why do you persist?"]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["You're wrong, <PERSON>. Together, we will find the"]}, {"code": 401, "indent": 0, "parameters": ["dark elves and thwart their plans."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["I'm warning you to turn back now."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["I will not hesistate to end you if you continue to defy"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>'s will."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["But I know how stubborn you are, \"old friend\"."]}, {"code": 250, "indent": 0, "parameters": [{"name": "Paralyze1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Glitch", "filterType": "glitch", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 0, "parameters": ["filterType = glitch"]}, {"code": 657, "indent": 0, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 0, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 0, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "enableFilter", "enableFilter", {"filterId": "Glitch", "activeness": "true"}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 0, "parameters": ["activeness = true"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 27, 'A', true)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 30, 'A', true)"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "eraseFilter", "eraseFilter", {"filterId": "Glitch"}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 26, 'A', false)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 29, 'A', false)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 1, 'A', true)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 20, 'A', true)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 22, 'A', true)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 31}, {"id": 22, "name": "Grave Left", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Statue03n", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["<Center>Here lies <PERSON><br>"]}, {"code": 401, "indent": 0, "parameters": ["1221-1222"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 37, "y": 46}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!doors", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"repeat": false, "skippable": true, "wait": true, "list": [{"code": 12}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 201, "indent": 0, "parameters": [0, 54, 8, 17, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 39, "y": 29}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "Evasion1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 14, "parameters": [0, 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Evasion1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 4], "indent": null}]}, {"code": 212, "indent": 0, "parameters": [-1, 283, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 25}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "ash_sheet_1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<shadow>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Black", "Duration:num": "60"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Black"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewPlayerLockedLight", "SPAWN LIGHT: Create Light(s) on Player", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"200\",\"intensity:num\":\"0.05\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Mysterious Man"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON>...\\! is that you?"]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Ash...\\! It's been far too long, old friend."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["So, you've finally decided to come to visit,"]}, {"code": 401, "indent": 0, "parameters": ["after all of these centuries..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 0, "parameters": ["Do you like what I've done with the place?"]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["You've lost your way, <PERSON>. Can't you see what"]}, {"code": 401, "indent": 0, "parameters": ["the Darkness has done to you? To your home?"]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["It is you who must see. Darkness isn't corruption."]}, {"code": 401, "indent": 1, "parameters": ["It's liberation."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["It frees me from the chains of mortality, the constraints of time."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["It gifts me with an unending life, to learn, grow, and"]}, {"code": 401, "indent": 1, "parameters": ["surpass limits that nature imposes."]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["It's a transcendent existence, <PERSON><PERSON><PERSON>. Isn't that worth"]}, {"code": 401, "indent": 1, "parameters": ["your comprehension, if not admiration?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["Indeed, <PERSON><PERSON><PERSON>. I see far more than I ever did. Darkness"]}, {"code": 401, "indent": 1, "parameters": ["isn't mere absence of light."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["It's potential, it's power... it's the canvas onto which a"]}, {"code": 401, "indent": 1, "parameters": ["new world can be drawn."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["I've surpassed the limits of the natural, becoming more than"]}, {"code": 401, "indent": 1, "parameters": ["I ever could. Don't you see?"]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2, "Ash"]}, {"code": 401, "indent": 1, "parameters": ["This isn't my end, <PERSON><PERSON><PERSON>. It's a new beginning."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Paralyze1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Glitch", "filterType": "glitch", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 0, "parameters": ["filterType = glitch"]}, {"code": 657, "indent": 0, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 0, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 0, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "enableFilter", "enableFilter", {"filterId": "Glitch", "activeness": "true"}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 0, "parameters": ["activeness = true"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 26, 'A', true)"]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(57, 29, 'A', true)"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "eraseFilter", "eraseFilter", {"filterId": "Glitch"}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Glitch"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What the..."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["It is the power of the Darkness to break the"]}, {"code": 401, "indent": 0, "parameters": ["rules of our reality."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What do you think the Realm of Corruption is?"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["It's the place where monsters come from, right?"]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["That much is true. But it's also a place"]}, {"code": 401, "indent": 0, "parameters": ["where the physics of our reality can be"]}, {"code": 401, "indent": 0, "parameters": ["rewritten."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["The Darkness allows for phenomena unseen in our"]}, {"code": 401, "indent": 0, "parameters": ["natural world."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["This... corruption, <PERSON> speaks of, is not a"]}, {"code": 401, "indent": 0, "parameters": ["corruption of evil. It's the corruption of the"]}, {"code": 401, "indent": 0, "parameters": ["natural order of things."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["This power, while terrifying, presents"]}, {"code": 401, "indent": 0, "parameters": ["possibilities that science and magic can only"]}, {"code": 401, "indent": 0, "parameters": ["dream of. But at what cost?"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 39, "y": 48}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 800>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 8, "pattern": 2, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 47}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 800>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 8, "pattern": 2, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 29}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 800>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 13}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 46}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 28}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 12}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewPlayerLockedLight", "SPAWN LIGHT: Create Light(s) on Player", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"200\",\"intensity:num\":\"0.05\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, null, null, null, null]}
{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "SmokeForest", "battleback2Name": "SmokeForest", "bgm": {"name": "DarkFantasyForestLoop3", "pan": 0, "pitch": 100, "volume": 70}, "bgs": {"name": "Multiple-Whispering-Voices", "pan": 0, "pitch": 100, "volume": 50}, "disableDashing": false, "displayName": "Smoke Forest", "encounterList": [], "encounterStep": 30, "height": 60, "note": "<Zoom: 200%>\n<Color Adjust Saturate: -0.25>\n<Fog 1 Settings>\n Name: !wispy_white_clouds\n Opacity: 25\n Horz Scroll: 1.0\n</Fog 1 Settings>\n<Bloom Threshold: 0.3>\n<Bloom Scale: 0.3>\n<Bloom Brightness: 1.5>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 19, "width": 60, "data": [2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2448, 2432, 2456, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2444, 2470, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2483, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2483, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2483, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2483, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 2466, 2452, 2435, 2452, 2468, 2856, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2466, 2452, 2452, 2433, 2432, 2432, 2432, 2434, 2452, 2452, 2452, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2440, 2432, 2432, 2432, 2432, 2432, 2436, 2460, 2460, 2460, 2470, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2472, 2460, 2460, 2440, 2432, 2436, 2470, 2850, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2852, 2472, 2460, 2470, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2828, 2846, 2849, 2849, 2849, 2845, 2828, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2816, 2816, 2820, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2854, 3715, 3713, 3717, 2860, 3715, 3713, 3713, 3713, 3725, 2848, 3723, 3717, 2856, 2844, 2844, 2844, 2844, 2824, 2820, 2844, 2854, 3722, 2832, 2816, 2816, 2840, 3726, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2844, 2844, 2844, 2844, 2844, 2824, 2820, 2844, 2844, 2844, 2844, 2844, 2828, 2854, 3714, 3700, 3701, 3713, 3719, 2858, 3721, 3713, 3719, 2859, 2849, 2849, 2838, 2819, 2852, 3721, 3713, 3713, 3713, 3713, 3717, 2856, 2854, 3715, 3713, 3707, 2856, 2844, 2844, 2846, 2853, 3722, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 3713, 3702, 3700, 3700, 3716, 2856, 2854, 3714, 3700, 3701, 3713, 3725, 2848, 3723, 3709, 3708, 3718, 2850, 2836, 2819, 2836, 2837, 2861, 2466, 2452, 2468, 2856, 2824, 2818, 2836, 2836, 2836, 2836, 2852, 3721, 3713, 3713, 3719, 2858, 3721, 3713, 3713, 3702, 3716, 2860, 3699, 3725, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2852, 3720, 3708, 3708, 3710, 3713, 3713, 3709, 3708, 3718, 2850, 2836, 2819, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2820, 2854, 2466, 2433, 2432, 2434, 2468, 2856, 2844, 2824, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2819, 2836, 2836, 2852, 3720, 3710, 3713, 3707, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2466, 2433, 2432, 2432, 2432, 2434, 2452, 2468, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2852, 3721, 3717, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2434, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3712, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2460, 2440, 2432, 2432, 2432, 2432, 2436, 2460, 2470, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 2472, 2440, 2432, 2436, 2460, 2470, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2472, 2440, 2456, 2859, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2448, 2434, 2452, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2444, 2460, 2470, 2832, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3722, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2483, 2850, 2836, 2817, 2816, 2816, 2816, 2840, 4451, 4454, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2483, 2856, 2844, 2828, 2844, 2844, 2844, 2854, 4449, 4452, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2466, 2435, 2452, 2468, 2848, 4787, 4786, 4786, 4790, 4457, 4460, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2466, 2452, 2433, 2432, 2436, 2470, 2848, 4785, 4784, 4784, 4788, 4787, 4790, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3722, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2472, 2440, 2432, 2432, 2456, 2850, 2842, 4793, 4792, 4792, 4796, 4793, 4796, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2824, 2818, 2852, 2472, 2460, 2460, 2470, 2832, 2818, 2836, 2852, 3722, 2859, 2838, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2830, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 3714, 3700, 3716, 2856, 2824, 2822, 2849, 2838, 2836, 2836, 2817, 2820, 2844, 2854, 3697, 3716, 2856, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2840, 3715, 3725, 2860, 3722, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 3723, 3709, 3708, 3710, 3717, 2856, 2841, 3722, 2856, 2844, 2844, 2828, 2854, 3715, 3713, 3709, 3710, 3713, 3713, 3713, 3713, 3713, 3713, 3717, 2856, 2844, 2854, 3712, 2858, 3723, 3719, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3714, 3701, 3725, 2850, 2836, 2836, 2852, 3721, 3717, 2848, 3721, 3713, 3713, 3725, 2848, 3723, 3719, 2850, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2852, 3721, 3713, 3713, 3713, 3719, 2833, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3696, 3704, 2850, 2817, 2816, 2816, 2818, 2852, 3712, 2833, 2836, 2836, 2836, 2836, 2819, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3720, 3705, 2832, 2816, 2816, 2816, 2820, 2854, 3712, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 3712, 2832, 2816, 2816, 2816, 2840, 3723, 3719, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3715, 3719, 2832, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3724, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3726, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3726, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2846, 2838, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3722, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3721, 3717, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 3712, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 3723, 3713, 3713, 3717, 2856, 2844, 2844, 2828, 2844, 2844, 2844, 2828, 2844, 2844, 2844, 2828, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3723, 3707, 2856, 2844, 2828, 2844, 2844, 2844, 2828, 2844, 2854, 3715, 3725, 2850, 2836, 2852, 3721, 3713, 3713, 3717, 2860, 3715, 3713, 3717, 2860, 3715, 3713, 3725, 2848, 3714, 3700, 3700, 3716, 2856, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3721, 3713, 3725, 2848, 3723, 3713, 3725, 2848, 3723, 3713, 3719, 2851, 2845, 2844, 2826, 2836, 2836, 2852, 3721, 3713, 3719, 2858, 3721, 3713, 3719, 2851, 2849, 2843, 3720, 3692, 3708, 3710, 3702, 3700, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2819, 2836, 2836, 2836, 2819, 2836, 2836, 2837, 2855, 2466, 2468, 2856, 2844, 2844, 2826, 2836, 2836, 2836, 2819, 2836, 2836, 2836, 2842, 3722, 2857, 2853, 3724, 2850, 2852, 3698, 3708, 3705, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 2466, 2433, 2434, 2452, 2452, 2468, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3697, 3716, 2857, 2849, 2845, 2854, 3724, 2858, 3721, 3725, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 2466, 2452, 2452, 2433, 2432, 2432, 2436, 2460, 2470, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3720, 3694, 3713, 3702, 3701, 3725, 2850, 2819, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2475, 2465, 2454, 2433, 2432, 2436, 2460, 2460, 2460, 2470, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3724, 2858, 3720, 3718, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 2472, 2460, 2460, 2470, 2850, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2837, 2847, 2849, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3714, 3701, 3725, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3696, 3704, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3720, 3690, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 3696, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3714, 3681, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3696, 3680, 3704, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3814, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3784, 3800, 0, 0, 0, 0, 3816, 3804, 3804, 3784, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3804, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3778, 3796, 3796, 3796, 3812, 0, 0, 0, 3792, 3778, 3812, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 3792, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3780, 3806, 3809, 3809, 3809, 3805, 3804, 3806, 3809, 3821, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3797, 3809, 3821, 0, 0, 3818, 0, 0, 0, 0, 0, 0, 0, 3819, 3821, 0, 3810, 3777, 3778, 3812, 0, 0, 0, 0, 0, 0, 0, 3819, 3798, 3796, 3796, 3781, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3809, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3793, 3796, 3812, 0, 0, 0, 3822, 0, 0, 0, 0, 3792, 3776, 3776, 3778, 3796, 3796, 3812, 0, 0, 0, 3818, 0, 3792, 3776, 3780, 3814, 0, 0, 0, 0, 3819, 3798, 3796, 3796, 3812, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3777, 3776, 3800, 0, 0, 0, 0, 3810, 3796, 3796, 3796, 3777, 3780, 3804, 3804, 3804, 3788, 3806, 3809, 3809, 3809, 3807, 3799, 3805, 3804, 3801, 0, 0, 0, 0, 0, 0, 3792, 3780, 3804, 3786, 3812, 0, 0, 0, 0, 3818, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3777, 3780, 3804, 3804, 3804, 3814, 0, 0, 0, 3820, 0, 0, 0, 0, 0, 3808, 0, 0, 3817, 3809, 3809, 3809, 3809, 3798, 3797, 3805, 3814, 0, 3792, 3778, 3796, 3812, 0, 3819, 3787, 3797, 3809, 3809, 3805, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3804, 3804, 3804, 3804, 3804, 3784, 3780, 3804, 3804, 3804, 3804, 3804, 3788, 3814, 0, 0, 0, 0, 0, 3818, 0, 0, 0, 3819, 3809, 3809, 3798, 3779, 3812, 0, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 3816, 3804, 3804, 3806, 3813, 0, 3816, 3801, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 0, 3810, 3796, 3779, 3796, 3797, 3821, 0, 0, 0, 3816, 3784, 3778, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 0, 3818, 0, 0, 0, 0, 0, 3820, 0, 0, 3793, 3796, 3797, 3821, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3810, 3796, 3779, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3779, 3796, 3796, 3812, 0, 0, 0, 0, 3819, 3785, 3776, 3800, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3812, 0, 0, 3792, 3776, 3778, 3812, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3801, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 3792, 3776, 3780, 3806, 3809, 3809, 3809, 3798, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 3794, 3804, 3814, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 0, 3793, 3796, 3812, 0, 0, 0, 0, 0, 0, 3811, 3809, 3805, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3819, 3803, 0, 0, 0, 0, 3810, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3778, 3797, 3809, 3809, 3805, 3804, 3806, 3813, 0, 0, 0, 3819, 3809, 3803, 0, 0, 0, 0, 3792, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3778, 3812, 0, 3817, 3798, 3812, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3795, 3821, 0, 0, 0, 0, 3817, 3798, 3796, 3796, 3796, 3777, 3800, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3782, 3821, 0, 3792, 3782, 3821, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3780, 3804, 3806, 3809, 3809, 3821, 0, 3819, 3815, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3780, 3804, 3806, 3809, 3798, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3800, 0, 3810, 3777, 3800, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3814, 0, 0, 0, 3792, 3780, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3818, 0, 3810, 3812, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 3816, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3800, 0, 3794, 3804, 3814, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 3811, 3809, 3798, 3796, 3797, 3809, 3798, 3797, 3815, 0, 3816, 3806, 3799, 3805, 3804, 3804, 3814, 0, 0, 3818, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3778, 3796, 3802, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3797, 3809, 3798, 3812, 0, 0, 0, 0, 3811, 3809, 3798, 3797, 3815, 0, 3816, 3804, 3801, 0, 3816, 3814, 0, 0, 0, 0, 3808, 0, 0, 0, 0, 0, 0, 3817, 3799, 3809, 3798, 3796, 3777, 3776, 3780, 3804, 3784, 3780, 3804, 3786, 3796, 3797, 3821, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 3816, 3806, 3799, 3809, 3798, 3797, 3815, 0, 3816, 3814, 3715, 3713, 3713, 3725, 3793, 3812, 0, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 0, 0, 0, 3722, 3820, 0, 3816, 3804, 3788, 3804, 3814, 0, 3792, 3800, 0, 3792, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 3723, 3713, 3713, 3717, 3820, 0, 3816, 3814, 3715, 3713, 3713, 3713, 3719, 3810, 3796, 3796, 3777, 3800, 0, 0, 0, 0, 0, 3810, 3802, 0, 0, 0, 0, 0, 0, 3721, 3713, 3713, 3713, 3725, 3808, 0, 0, 3810, 3777, 3800, 0, 3816, 3788, 3814, 0, 3819, 3798, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3812, 3721, 3713, 3713, 3713, 3713, 3719, 3810, 3796, 3796, 3797, 3805, 3804, 3804, 3784, 3778, 3812, 0, 0, 0, 0, 3792, 3778, 3796, 3812, 0, 3819, 3809, 3809, 3798, 3796, 3796, 3796, 3796, 3802, 0, 3811, 3805, 3804, 3790, 3821, 0, 3808, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3797, 3809, 3809, 3821, 0, 3810, 3777, 3776, 3780, 3814, 0, 0, 0, 3816, 3784, 3782, 3809, 3798, 3796, 3796, 3777, 3780, 3804, 3814, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3804, 3786, 3796, 3802, 0, 0, 3820, 0, 3810, 3779, 3812, 0, 3819, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3794, 3804, 3804, 3814, 0, 0, 0, 0, 0, 3816, 3801, 0, 3816, 3804, 3804, 3788, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3814, 0, 3818, 0, 0, 3792, 3780, 3806, 3821, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3802, 0, 0, 0, 3810, 3796, 3796, 3812, 0, 0, 3808, 0, 0, 0, 0, 3808, 0, 0, 3810, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 0, 0, 3793, 3796, 3796, 3781, 3814, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3806, 3821, 0, 3816, 3801, 0, 3819, 3798, 3777, 3776, 3776, 3778, 3812, 0, 3793, 3797, 3821, 0, 3810, 3779, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3808, 0, 0, 3792, 3776, 3776, 3776, 3780, 3814, 0, 3792, 3800, 0, 3819, 3805, 3804, 3784, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3804, 3804, 3788, 3804, 3804, 3804, 3804, 3788, 3804, 3804, 3784, 3778, 3797, 3809, 3809, 3809, 3805, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3783, 3821, 0, 3792, 3776, 3776, 3776, 3800, 0, 0, 3794, 3814, 0, 0, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3808, 0, 0, 0, 0, 3820, 0, 0, 3792, 3780, 3814, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 3819, 3805, 3801, 0, 0, 3792, 3776, 3776, 3776, 3778, 3796, 3796, 3802, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3778, 3812, 0, 0, 0, 0, 3810, 3779, 3812, 0, 0, 3818, 0, 0, 3810, 3777, 3800, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3808, 0, 3810, 3777, 3776, 3776, 3776, 3780, 3804, 3804, 3806, 3809, 3809, 3809, 3809, 3805, 3804, 3784, 3780, 3804, 3806, 3809, 3809, 3809, 3809, 3785, 3780, 3806, 3809, 3809, 3807, 3809, 3809, 3785, 3776, 3778, 3796, 3797, 3809, 3809, 3805, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 0, 3810, 3783, 3809, 3785, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 0, 3816, 3814, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3801, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3777, 3800, 0, 3816, 3784, 3776, 3776, 3776, 3778, 3797, 3809, 3798, 3796, 3797, 3809, 3798, 3812, 0, 0, 0, 0, 3811, 3809, 3798, 3812, 0, 0, 0, 0, 3811, 3809, 3798, 3812, 0, 0, 0, 0, 0, 3817, 3809, 3809, 3809, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3812, 0, 3792, 3776, 3776, 3776, 3780, 3814, 0, 3816, 3804, 3814, 0, 3816, 3806, 3799, 3809, 3798, 3797, 3815, 0, 3816, 3806, 3799, 3809, 3798, 3797, 3815, 0, 3816, 3806, 3799, 3809, 3798, 3812, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3806, 3798, 3777, 3776, 3776, 3776, 3800, 3723, 3713, 3713, 3713, 3713, 3713, 3713, 3717, 3820, 0, 3816, 3814, 3715, 3713, 3713, 3717, 3820, 0, 3816, 3814, 3715, 3713, 3713, 3717, 3820, 0, 3816, 3806, 3799, 3809, 3798, 3812, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3816, 3784, 3776, 3776, 3776, 3778, 3797, 3809, 3799, 3809, 3809, 3798, 3812, 3721, 3713, 3713, 3713, 3713, 3719, 3819, 3821, 3721, 3713, 3713, 3713, 3713, 3719, 3819, 3821, 3721, 3713, 3713, 3713, 3717, 3820, 0, 3816, 3806, 3799, 3809, 3798, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3801, 0, 0, 3794, 3804, 3804, 3804, 3804, 3801, 0, 3808, 0, 0, 3794, 3806, 3809, 3809, 3809, 3798, 3796, 3812, 0, 0, 3810, 3796, 3796, 3796, 3796, 3812, 0, 0, 3810, 3796, 3796, 3812, 3721, 3713, 3713, 3713, 3717, 3820, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 3795, 3821, 0, 3808, 0, 0, 0, 0, 3793, 3796, 3779, 3797, 3809, 3815, 0, 0, 0, 0, 3816, 3804, 3806, 3799, 3809, 3805, 3804, 3788, 3804, 3804, 3806, 3799, 3809, 3805, 3804, 3804, 3786, 3796, 3796, 3796, 3812, 3721, 3713, 3713, 3713, 3725, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3802, 0, 0, 3817, 3809, 3799, 3809, 3809, 3805, 3788, 3804, 3814, 0, 0, 3810, 3796, 3812, 0, 0, 0, 0, 3820, 0, 0, 0, 3820, 0, 0, 0, 3808, 0, 0, 0, 0, 3816, 3804, 3804, 3784, 3778, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3778, 3812, 0, 0, 0, 3808, 0, 0, 0, 3808, 0, 0, 0, 3811, 3805, 3804, 3786, 3796, 3796, 3812, 0, 0, 0, 3818, 0, 0, 0, 3811, 3809, 3803, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3778, 3796, 3796, 3796, 3779, 3796, 3796, 3796, 3779, 3796, 3796, 3797, 3815, 0, 0, 3816, 3804, 3804, 3786, 3796, 3796, 3796, 3779, 3796, 3796, 3796, 3802, 0, 3817, 3813, 0, 3810, 3812, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3817, 3809, 3805, 3814, 0, 3818, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3797, 3809, 3805, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3810, 3779, 3796, 3796, 3781, 3804, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3778, 3812, 0, 3818, 0, 0, 3810, 3777, 3776, 3776, 3780, 3814, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3804, 3804, 3786, 3796, 3796, 3796, 3812, 0, 0, 0, 0, 0, 0, 0, 3818, 0, 0, 0, 0, 3810, 3796, 3796, 3796, 3796, 3812, 0, 0, 0, 3816, 3784, 3776, 3776, 3776, 3776, 3778, 3797, 3807, 3809, 3809, 3785, 3776, 3776, 3780, 3814, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 3816, 3804, 3784, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3779, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3780, 3804, 3806, 3821, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 3816, 3804, 3784, 3776, 3776, 3776, 3780, 3804, 3804, 3804, 3804, 3784, 3776, 3776, 3776, 3780, 3804, 3804, 3814, 0, 0, 0, 0, 0, 3810, 3777, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 3819, 3785, 3776, 3776, 3800, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3780, 3814, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 3816, 3804, 3804, 3804, 3814, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3805, 3804, 3804, 3804, 3804, 3784, 3776, 3800, 0, 0, 0, 3792, 3776, 3780, 3806, 3809, 3809, 3809, 3809, 3785, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3782, 3821, 0, 0, 3792, 3776, 3800, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3812, 0, 3810, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3800, 0, 0, 0, 3792, 3776, 3778, 3796, 3796, 3796, 3796, 3796, 3777, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3800, 0, 0, 0, 3792, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 3776, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 169, 169, 0, 169, 169, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 229, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 169, 169, 169, 169, 169, 169, 226, 226, 226, 226, 226, 226, 226, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 237, 237, 237, 237, 0, 0, 0, 0, 0, 237, 237, 237, 237, 245, 237, 0, 237, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 568, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 245, 245, 245, 237, 237, 237, 237, 245, 245, 245, 245, 245, 0, 237, 237, 0, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 237, 237, 237, 237, 237, 237, 0, 0, 0, 0, 0, 0, 0, 237, 237, 237, 237, 576, 577, 578, 0, 0, 0, 0, 0, 0, 0, 0, 587, 588, 589, 590, 591, 0, 573, 572, 0, 245, 245, 245, 245, 0, 0, 0, 0, 0, 0, 254, 238, 238, 238, 0, 226, 226, 226, 226, 226, 226, 226, 226, 245, 245, 245, 245, 245, 245, 245, 237, 237, 237, 237, 237, 237, 245, 245, 245, 245, 584, 585, 586, 0, 0, 0, 0, 0, 0, 0, 0, 595, 596, 597, 598, 0, 0, 0, 0, 0, 572, 573, 572, 573, 557, 0, 0, 0, 0, 0, 0, 254, 238, 238, 0, 0, 226, 226, 226, 226, 226, 226, 226, 0, 573, 572, 573, 572, 0, 0, 245, 245, 245, 245, 245, 245, 0, 573, 572, 0, 592, 593, 594, 0, 556, 557, 0, 0, 0, 0, 0, 603, 604, 605, 606, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 238, 0, 0, 0, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 572, 573, 0, 0, 0, 0, 600, 601, 602, 0, 564, 565, 0, 0, 0, 0, 0, 611, 612, 613, 614, 615, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 0, 0, 0, 0, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 578, 552, 556, 557, 556, 512, 513, 0, 0, 0, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 586, 560, 564, 565, 564, 0, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 0, 0, 254, 0, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 565, 564, 565, 254, 226, 226, 226, 226, 0, 0, 0, 0, 512, 512, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 573, 572, 573, 220, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 684, 685, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 572, 573, 572, 573, 572, 220, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 556, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 0, 0, 0, 0, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 564, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 0, 0, 565, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 557, 556, 557, 556, 560, 0, 562, 563, 0, 0, 552, 553, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 565, 564, 0, 0, 0, 0, 0, 0, 560, 561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 572, 573, 572, 573, 0, 0, 556, 557, 0, 557, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 0, 0, 0, 0, 0, 0, 564, 565, 0, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 229, 0, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 229, 229, 229, 229, 229, 229, 229, 229, 245, 0, 245, 245, 229, 0, 0, 0, 0, 0, 0, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 229, 229, 229, 229, 0, 0, 0, 0, 229, 229, 229, 229, 245, 253, 245, 245, 245, 253, 245, 245, 0, 0, 0, 0, 245, 992, 993, 0, 0, 0, 0, 245, 229, 229, 229, 229, 0, 0, 0, 742, 743, 0, 0, 0, 0, 0, 0, 0, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 245, 253, 245, 245, 229, 229, 229, 229, 245, 253, 245, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 253, 245, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 0, 245, 253, 245, 245, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 219, 0, 0, 565, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 572, 573, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 565, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 556, 564, 0, 0, 0, 0, 0, 0, 565, 564, 565, 564, 0, 0, 564, 556, 564, 0, 0, 564, 0, 0, 0, 0, 564, 565, 564, 0, 0, 238, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 564, 572, 0, 0, 0, 0, 0, 0, 573, 572, 573, 572, 0, 0, 572, 564, 572, 0, 0, 572, 0, 0, 0, 0, 572, 573, 572, 0, 254, 238, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 565, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 0, 0, 254, 238, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 573, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 0, 0, 254, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 513, 514, 0, 0, 0, 0, 0, 0, 0, 0, 515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 564, 565, 564, 556, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 229, 229, 229, 229, 229, 229, 229, 229, 0, 0, 0, 0, 229, 229, 229, 229, 0, 0, 0, 0, 229, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 253, 245, 245, 245, 253, 245, 245, 229, 229, 229, 229, 245, 253, 245, 245, 229, 229, 229, 229, 245, 253, 245, 245, 229, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 245, 253, 245, 245, 0, 556, 557, 0, 245, 253, 245, 245, 0, 556, 557, 0, 245, 253, 245, 245, 229, 229, 229, 229, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 245, 253, 245, 245, 229, 229, 229, 229, 226, 226, 226, 226, 226, 226, 226, 226, 238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 245, 253, 245, 245, 226, 226, 226, 226, 226, 226, 226, 226, 238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 238, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 0, 0, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 564, 565, 564, 565, 0, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 0, 0, 217, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 572, 573, 572, 573, 0, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 0, 0, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 0, 0, 573, 572, 573, 557, 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 565, 557, 556, 557, 0, 556, 557, 352, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 565, 0, 0, 0, 21, 22, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 564, 220, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 226, 223, 0, 0, 572, 0, 0, 0, 0, 573, 572, 573, 565, 564, 565, 0, 0, 0, 0, 0, 360, 0, 0, 0, 0, 0, 0, 573, 572, 573, 565, 557, 0, 29, 30, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 220, 226, 226, 226, 226, 226, 226, 226, 236, 236, 236, 236, 512, 565, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 572, 573, 0, 0, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 0, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 565, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 572, 573, 572, 573, 572, 573, 0, 0, 557, 556, 0, 0, 557, 556, 557, 556, 0, 0, 0, 0, 0, 557, 556, 557, 556, 0, 572, 573, 0, 565, 572, 573, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 512, 512, 512, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 564, 0, 0, 565, 564, 565, 564, 0, 0, 0, 0, 0, 565, 564, 565, 564, 0, 0, 0, 0, 573, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 512, 512, 512, 512, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 512, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 229, 229, 0, 229, 229, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 237, 237, 0, 237, 237, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 237, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 229, 229, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 237, 237, 237, 237, 237, 237, 237, 0, 237, 237, 237, 237, 237, 237, 237, 237, 556, 557, 556, 557, 237, 237, 237, 237, 237, 0, 0, 0, 0, 237, 512, 237, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 237, 237, 237, 237, 237, 237, 229, 229, 229, 229, 229, 229, 229, 237, 237, 237, 237, 237, 237, 237, 237, 237, 245, 245, 0, 245, 245, 237, 237, 237, 237, 237, 245, 564, 565, 564, 565, 556, 557, 556, 557, 237, 0, 0, 0, 0, 245, 0, 0, 238, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 556, 557, 556, 557, 237, 237, 237, 237, 237, 237, 237, 556, 557, 556, 557, 245, 245, 245, 245, 245, 0, 0, 0, 0, 0, 245, 245, 245, 245, 245, 0, 572, 556, 557, 573, 564, 565, 564, 565, 245, 0, 0, 0, 0, 0, 512, 513, 514, 515, 225, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 564, 565, 564, 565, 237, 556, 557, 556, 557, 556, 557, 564, 565, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 318, 319, 564, 565, 556, 557, 556, 557, 556, 309, 0, 0, 0, 0, 0, 520, 521, 522, 523, 238, 225, 169, 169, 169, 169, 169, 169, 169, 572, 556, 557, 556, 557, 573, 245, 564, 565, 564, 565, 564, 565, 572, 556, 557, 573, 0, 0, 0, 0, 684, 685, 0, 0, 0, 556, 557, 0, 0, 0, 0, 326, 327, 304, 305, 564, 565, 564, 565, 564, 565, 556, 557, 0, 0, 0, 528, 529, 530, 531, 238, 238, 225, 169, 169, 169, 169, 169, 169, 0, 564, 565, 564, 565, 0, 0, 572, 556, 557, 556, 557, 0, 0, 564, 565, 0, 0, 0, 0, 0, 692, 693, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 0, 312, 313, 572, 573, 572, 573, 572, 573, 564, 565, 0, 0, 0, 536, 537, 538, 539, 238, 238, 238, 225, 169, 169, 169, 169, 169, 0, 572, 573, 572, 573, 516, 517, 518, 564, 565, 564, 565, 0, 0, 572, 573, 0, 0, 0, 0, 0, 700, 701, 0, 0, 0, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 0, 0, 570, 544, 545, 546, 547, 254, 238, 238, 238, 225, 169, 169, 169, 169, 0, 0, 0, 0, 0, 524, 525, 526, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 0, 556, 557, 553, 554, 555, 557, 254, 238, 238, 238, 169, 169, 169, 169, 0, 0, 0, 0, 0, 532, 533, 534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 35, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 564, 565, 561, 562, 563, 565, 0, 254, 238, 238, 169, 169, 169, 169, 0, 0, 0, 0, 0, 540, 541, 542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 35, 36, 0, 0, 0, 0, 0, 0, 42, 43, 44, 34, 35, 36, 512, 513, 514, 515, 0, 0, 0, 572, 573, 0, 0, 0, 0, 572, 573, 572, 573, 572, 573, 557, 556, 557, 238, 169, 169, 169, 169, 0, 0, 0, 0, 0, 548, 549, 550, 0, 0, 512, 513, 0, 515, 0, 0, 0, 0, 42, 43, 44, 0, 0, 0, 0, 0, 0, 50, 51, 52, 42, 43, 44, 520, 521, 522, 523, 0, 0, 512, 513, 514, 515, 0, 0, 0, 0, 0, 0, 564, 556, 557, 556, 557, 556, 557, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 520, 521, 522, 523, 0, 0, 0, 0, 50, 51, 52, 0, 0, 0, 0, 0, 0, 58, 59, 60, 50, 51, 52, 528, 529, 530, 531, 0, 0, 520, 521, 522, 523, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 565, 564, 565, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 530, 531, 0, 684, 685, 556, 58, 59, 60, 0, 0, 352, 0, 352, 0, 0, 0, 0, 58, 59, 60, 536, 537, 538, 539, 0, 0, 528, 529, 530, 531, 0, 0, 0, 0, 0, 0, 0, 557, 556, 557, 556, 557, 573, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 538, 539, 0, 692, 693, 557, 556, 557, 0, 0, 352, 360, 352, 360, 352, 0, 0, 0, 692, 693, 0, 544, 545, 546, 547, 0, 0, 536, 537, 538, 539, 0, 0, 0, 0, 0, 556, 557, 556, 557, 565, 564, 565, 220, 169, 169, 169, 169, 0, 0, 0, 0, 556, 557, 556, 557, 0, 0, 544, 545, 546, 547, 0, 700, 701, 565, 564, 565, 0, 0, 360, 0, 360, 0, 360, 0, 0, 556, 700, 701, 557, 552, 553, 554, 555, 0, 0, 544, 545, 546, 547, 0, 0, 0, 0, 0, 564, 565, 564, 565, 573, 572, 573, 217, 169, 169, 169, 169, 169, 169, 169, 219, 564, 556, 557, 556, 557, 0, 552, 553, 554, 555, 0, 0, 572, 556, 557, 556, 557, 556, 557, 0, 352, 0, 0, 0, 556, 564, 565, 564, 565, 557, 561, 556, 557, 556, 557, 556, 557, 554, 555, 0, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 564, 565, 564, 565, 0, 560, 561, 562, 563, 556, 557, 556, 557, 565, 564, 565, 564, 565, 0, 360, 352, 0, 0, 564, 572, 573, 572, 573, 565, 0, 564, 565, 564, 565, 564, 565, 562, 563, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 556, 557, 556, 557, 0, 684, 685, 556, 564, 565, 564, 565, 573, 572, 573, 572, 573, 0, 0, 360, 0, 0, 572, 573, 572, 573, 572, 573, 0, 572, 573, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 565, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 564, 565, 564, 565, 0, 692, 693, 564, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 976, 977, 978, 979, 0, 0, 0, 564, 565, 564, 565, 715, 716, 717, 718, 719, 0, 0, 0, 556, 557, 556, 557, 573, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 573, 572, 573, 572, 573, 0, 700, 701, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 984, 985, 986, 987, 771, 0, 0, 572, 573, 572, 573, 723, 724, 725, 726, 727, 0, 0, 0, 564, 565, 564, 565, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 0, 0, 0, 984, 985, 986, 987, 0, 0, 0, 556, 557, 0, 0, 731, 732, 733, 734, 735, 0, 0, 0, 572, 556, 557, 556, 557, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 556, 557, 0, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 564, 565, 564, 565, 0, 0, 0, 0, 0, 0, 644, 645, 646, 994, 995, 0, 0, 0, 0, 556, 557, 0, 739, 740, 741, 316, 317, 0, 0, 0, 0, 564, 565, 564, 565, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 0, 564, 565, 0, 0, 0, 0, 0, 0, 564, 565, 0, 231, 308, 572, 573, 572, 573, 0, 0, 568, 569, 570, 0, 652, 653, 654, 884, 0, 877, 877, 0, 0, 564, 565, 0, 0, 0, 0, 324, 325, 0, 0, 0, 556, 557, 556, 557, 573, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 219, 572, 556, 557, 0, 0, 0, 0, 556, 557, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 576, 577, 578, 0, 660, 661, 662, 892, 0, 0, 0, 0, 556, 557, 556, 309, 0, 318, 319, 0, 0, 0, 0, 0, 564, 565, 564, 565, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 219, 564, 565, 556, 557, 556, 557, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584, 585, 586, 0, 668, 669, 670, 512, 0, 789, 790, 0, 564, 565, 564, 565, 0, 326, 327, 0, 0, 0, 0, 0, 572, 573, 572, 573, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 573, 564, 556, 557, 565, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 592, 593, 594, 0, 0, 677, 678, 512, 512, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 564, 565, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 600, 601, 602, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 565, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 556, 557, 556, 564, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 565, 573, 238, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 564, 556, 557, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 556, 557, 556, 557, 556, 557, 0, 684, 685, 556, 557, 0, 556, 557, 0, 0, 572, 556, 557, 556, 557, 238, 238, 225, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 564, 565, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 565, 557, 565, 34, 35, 36, 0, 564, 556, 557, 556, 557, 565, 0, 692, 693, 557, 565, 556, 557, 565, 0, 0, 556, 557, 556, 557, 565, 238, 570, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 556, 557, 556, 557, 0, 0, 0, 0, 0, 477, 478, 0, 0, 0, 0, 564, 565, 573, 565, 573, 42, 43, 44, 0, 572, 564, 565, 564, 565, 573, 0, 700, 701, 565, 573, 564, 565, 573, 0, 0, 564, 565, 564, 565, 573, 586, 578, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 564, 556, 557, 565, 0, 0, 0, 0, 0, 463, 463, 556, 557, 556, 557, 572, 573, 572, 573, 0, 50, 51, 52, 557, 0, 572, 573, 572, 573, 0, 0, 0, 572, 573, 0, 572, 573, 34, 35, 36, 572, 556, 557, 556, 557, 0, 586, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 572, 564, 565, 573, 0, 0, 0, 0, 0, 0, 0, 564, 565, 564, 565, 0, 0, 0, 0, 0, 58, 59, 60, 565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43, 44, 0, 564, 565, 564, 565, 557, 0, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 512, 572, 573, 515, 0, 0, 0, 0, 512, 513, 514, 572, 573, 572, 573, 0, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 51, 52, 556, 557, 573, 572, 573, 565, 557, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 520, 521, 522, 523, 0, 0, 0, 0, 520, 521, 522, 523, 0, 0, 556, 557, 556, 557, 556, 557, 0, 0, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 556, 557, 556, 557, 0, 58, 59, 60, 564, 565, 572, 573, 572, 573, 565, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 0, 528, 529, 530, 531, 0, 0, 0, 0, 528, 529, 530, 531, 0, 0, 564, 565, 564, 565, 564, 565, 556, 557, 556, 557, 564, 565, 564, 565, 556, 557, 556, 557, 564, 565, 564, 565, 556, 557, 556, 557, 572, 573, 0, 0, 0, 572, 573, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 0, 536, 537, 538, 539, 0, 0, 0, 0, 536, 537, 538, 539, 0, 0, 572, 573, 572, 573, 572, 573, 564, 565, 564, 565, 572, 573, 572, 573, 564, 565, 564, 565, 572, 573, 572, 573, 564, 565, 564, 565, 556, 557, 556, 557, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 227, 238, 238, 0, 544, 545, 546, 547, 0, 0, 0, 0, 544, 545, 546, 547, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 572, 573, 572, 573, 564, 565, 564, 565, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 223, 238, 238, 0, 552, 553, 554, 555, 0, 0, 0, 0, 552, 553, 554, 555, 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 572, 573, 0, 0, 0, 0, 169, 169, 169, 169, 169, 169, 169, 169, 223, 238, 252, 0, 560, 561, 562, 563, 0, 0, 0, 0, 560, 561, 562, 563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 516, 517, 518, 0, 0, 0, 0, 556, 557, 556, 557, 0, 217, 169, 169, 169, 169, 169, 169, 169, 169, 223, 556, 557, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 524, 525, 526, 0, 0, 0, 556, 557, 565, 564, 565, 217, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 556, 557, 556, 557, 684, 685, 0, 0, 0, 0, 0, 0, 0, 34, 35, 36, 0, 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 532, 533, 534, 0, 0, 0, 564, 565, 573, 572, 573, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 564, 565, 564, 565, 564, 565, 692, 693, 0, 0, 0, 0, 0, 0, 0, 42, 43, 44, 0, 0, 0, 0, 352, 360, 352, 0, 0, 0, 0, 0, 3, 4, 5, 6, 7, 0, 0, 0, 0, 540, 541, 542, 0, 0, 0, 572, 573, 557, 220, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 572, 573, 572, 556, 557, 556, 700, 701, 557, 556, 557, 0, 0, 556, 557, 50, 51, 52, 0, 0, 0, 0, 360, 360, 360, 556, 557, 556, 557, 0, 11, 12, 13, 14, 15, 0, 0, 0, 0, 548, 549, 550, 34, 35, 36, 0, 564, 565, 557, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 223, 0, 0, 564, 565, 564, 565, 564, 556, 557, 556, 0, 0, 564, 565, 58, 59, 60, 0, 352, 352, 556, 557, 556, 557, 564, 556, 557, 556, 557, 19, 20, 34, 35, 36, 0, 0, 0, 0, 0, 0, 0, 42, 43, 44, 0, 572, 573, 565, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 169, 556, 557, 556, 557, 573, 572, 573, 572, 564, 565, 564, 0, 0, 572, 573, 564, 565, 360, 352, 352, 360, 564, 565, 564, 565, 572, 564, 565, 564, 556, 27, 28, 42, 43, 44, 0, 0, 0, 0, 0, 0, 0, 50, 51, 52, 0, 564, 572, 573, 169, 169, 169, 169, 169, 169, 169, 229, 229, 229, 229, 564, 556, 557, 556, 557, 556, 557, 0, 572, 573, 572, 573, 572, 573, 0, 572, 573, 0, 360, 360, 0, 572, 573, 572, 573, 0, 572, 556, 557, 564, 565, 556, 50, 51, 52, 0, 0, 0, 0, 0, 0, 0, 58, 59, 60, 0, 572, 573, 512, 229, 229, 229, 229, 229, 229, 229, 236, 236, 236, 236, 572, 564, 565, 564, 565, 564, 565, 0, 0, 556, 557, 0, 0, 556, 557, 556, 557, 0, 0, 0, 0, 0, 556, 557, 556, 557, 0, 564, 565, 556, 557, 564, 58, 59, 60, 0, 0, 0, 512, 512, 512, 0, 0, 0, 556, 557, 512, 512, 512, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 556, 557, 556, 557, 556, 557, 556, 557, 556, 564, 565, 557, 556, 564, 565, 564, 565, 557, 0, 0, 0, 556, 564, 565, 564, 565, 557, 556, 557, 564, 556, 557, 556, 557, 565, 0, 0, 0, 512, 512, 0, 0, 0, 0, 564, 565, 557, 556, 557, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 564, 565, 564, 565, 564, 565, 564, 565, 564, 572, 573, 565, 564, 572, 573, 572, 573, 565, 0, 0, 0, 564, 572, 573, 572, 573, 565, 564, 565, 572, 564, 565, 564, 565, 573, 0, 0, 0, 512, 512, 512, 0, 0, 0, 572, 573, 565, 564, 565, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 572, 573, 0, 0, 512, 572, 573, 572, 573, 572, 573, 572, 573, 0, 572, 573, 572, 573, 512, 0, 0, 0, 0, 512, 512, 0, 0, 0, 512, 572, 573, 572, 573, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 0, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 229, 0, 0, 0, 512, 512, 512, 0, 0, 0, 229, 229, 229, 229, 229, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 0, 0, 512, 512, 512, 0, 0, 0, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Black", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Black"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewPlayerLockedLight", "SPAWN LIGHT: Create Light(s) on Player", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"200\",\"intensity:num\":\"0.05\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Water_Mist", "WATER: Mist", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"15\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"30\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.2\\\",\\\"scaleRatioY:num\\\":\\\"0.3\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#888888\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "DARK: <PERSON>", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"180\\\",\\\"opacityVariance:num\\\":\\\"40\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"30\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"2\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"45\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"-3\\\",\\\"spinSpeedVariance:eval\\\":\\\"2\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeClouds", "DARK: Smoke Clouds", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"5\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"128\\\",\\\"opacityVariance:num\\\":\\\"24\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#00e1e1\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0.7\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"5\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 125, 0, 100, 100, 0, 1, 60, false, 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 1, 3]}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 0]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\",\"4\"]", "UpperLower:str": "both", "Duration:eval": "60", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"3\",\"4\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Dark Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Dark Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 0"]}, {"code": 355, "indent": 1, "parameters": ["setSelfSwitchValue(58, 3, 'A', false)"]}, {"code": 230, "indent": 1, "parameters": [360]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 111, "indent": 1, "parameters": [1, 119, 1, 120, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "evil eye", 0, 0, 130, 0, 100, 100, 0, 1]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 130, 0, 100, 100, 100, 1, 60, false, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_BloodRain", "DARK: Blood Rain", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"36\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"5\\\",\\\"totalPerPower:num\\\":\\\"5\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#cc0000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"6\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"12\\\",\\\"angle:eval\\\":\\\"255\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"5\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"36\\…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Slow_Icons_Mid", "SLOW: Flying Icons ●", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"50\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"InQuart\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.21\\\",\\\"scaleVariance:num\\\":\\\"0.2\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"false\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[\\\\\\\"evil eye\\\\\\\"]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"frozen\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"1\\\",\\\"ySwaySpeed:eval\\\":\\\"0\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Red", "Duration:num": "60"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Red"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.5", "Duration:num": "360", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 360"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(58, 3, 'A', true)"]}, {"code": 230, "indent": 0, "parameters": [300]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.25", "Duration:num": "60", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.25"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gamePlayer.isMoving()"]}, {"code": 250, "indent": 1, "parameters": [{"name": "EVFXForge10_09_DreadforgeAura", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 170], 60, false]}, {"code": 225, "indent": 1, "parameters": [5, 5, 30, false]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, -1350, -950, 375, 375, 255, 0, 45, true, 1]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 301, "indent": 1, "parameters": [0, 71, false, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 15, "pitch": 120, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 2}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 500>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 17}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 16}, {"id": 6, "name": "EV006", "note": "<Hitbox Right: 8>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 58, 1, 17, 6, 0]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Feels like we're going in circles...\\! maybe"]}, {"code": 401, "indent": 0, "parameters": ["we need to investigate the area?"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 46, 42, 1, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 39, "y": 59}, {"id": 7, "name": "EV007", "note": "<Hitbox Right: 8>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 46, 21, 1, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 59}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 21}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 20}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 21}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 13}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 44}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 30>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: -20, +10>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset X: +42>"]}, {"code": 408, "indent": 0, "parameters": ["<Sprite Offset Y: +35>"]}, {"code": 108, "indent": 0, "parameters": ["<Hitbox Right: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Down: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<Hitbox Left: 1>"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeFog", "DARK: Smog", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "10", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"32\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"0\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#222222\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"12\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"event\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"-24\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.5\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Devil3", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 301, "indent": 0, "parameters": [0, 70, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"3\"]", "UpperLower:str": "both", "Duration:eval": "10", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 10"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 44}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark grey>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: 0, -15>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 29}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 500>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 37}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 36}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 500>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 53}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 52}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Object06", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: grey>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 15>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Check underneath the loose rock?"]}, {"code": 102, "indent": 0, "parameters": [["Yes", "No"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Yes"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 1, "parameters": [109, 0, 0, 1]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 101, "indent": 1, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["It's actually here! Where I saw him put it.."]}, {"code": 101, "indent": 1, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["I remember when I came by that day with a new"]}, {"code": 401, "indent": 1, "parameters": ["elixir."]}, {"code": 101, "indent": 1, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Something seemed off about him. He wouldn't"]}, {"code": 401, "indent": 1, "parameters": ["look me in the eye anymore..."]}, {"code": 101, "indent": 1, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["So you saw all of that in your vision? Very,"]}, {"code": 401, "indent": 1, "parameters": ["very interesting.. I have never heard of a"]}, {"code": 401, "indent": 1, "parameters": ["power like this before."]}, {"code": 101, "indent": 1, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Perhaps you should return to the painting now?"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "No"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 29}, {"id": 20, "name": "EV020", "note": "<Tint Color: 0, 0, 0, 255>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["This is the place where <PERSON> went in my vision."]}, {"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Did he recite any encantations?"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I'll try to remember..."]}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 0]}, {"code": 102, "indent": 0, "parameters": [["Telemus", "Telechus", "<PERSON>ev<PERSON>"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Telemus"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Telechus"]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON>ev<PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 102, "indent": 0, "parameters": [["Omnirum", "Omnilux", "<PERSON><PERSON><PERSON><PERSON>"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Omnirum"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Omnilux"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 102, "indent": 0, "parameters": [["So<PERSON><PERSON><PERSON>", "Somanence", "<PERSON><PERSON><PERSON>"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "So<PERSON><PERSON><PERSON>"]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "Somanence"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<PERSON><PERSON><PERSON>"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 119, 0, 3, 0]}, {"code": 101, "indent": 1, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Telechus Omnisus Somulence!"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Hmmm, no. That doesn't seem right."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Gate05ash", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapEventEffects", "Flash_ChangeFor_Player", "Flash: Change for Player", {"Color": "", "ColorRed:eval": "0", "ColorGreen:eval": "255", "ColorBlue:eval": "255", "ColorAlpha:eval": "255", "Duration:eval": "360", "Cycle:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Flash Color = "]}, {"code": 657, "indent": 0, "parameters": ["Red = 0"]}, {"code": 657, "indent": 0, "parameters": ["Green = 255"]}, {"code": 657, "indent": 0, "parameters": ["Blue = 255"]}, {"code": 657, "indent": 0, "parameters": ["Alpha = 255"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 360"]}, {"code": 657, "indent": 0, "parameters": ["Cycle = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.5", "Duration:num": "60", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 225, "indent": 0, "parameters": [1, 9, 180, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Gate1", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 212, "indent": 0, "parameters": [0, 318, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [30], "indent": null}, {"code": 18}, {"code": 15, "parameters": [30], "indent": null}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Gate05ash", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 80>"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 17}, {"code": 15, "parameters": [30], "indent": null}, {"code": 18}, {"code": 15, "parameters": [30], "indent": null}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1", "Duration:num": "60", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 101, "indent": 0, "parameters": ["orman-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Woah! It actually worked...\\! maybe you're not"]}, {"code": 401, "indent": 0, "parameters": ["crazy!"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["I'm not so sure anymore..."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Shall we go inside?"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Gate05ash", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: dark cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 80>"]}, {"code": 201, "indent": 0, "parameters": [0, 153, 19, 16, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["setSelfSwitchValue(165, 5, 'D', true)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 44, "y": 8}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 500>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 20%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 20%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 12}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!doors", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["mondrus-face", 0, 0, 2, "<PERSON><PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["This was <PERSON>'s old house. I sense a strange"]}, {"code": 401, "indent": 0, "parameters": ["presence from within."]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"repeat": false, "skippable": true, "wait": true, "list": [{"code": 12}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 201, "indent": 0, "parameters": [0, 53, 8, 17, 0, 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$!doors", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"repeat": false, "skippable": true, "wait": true, "list": [{"code": 12}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 201, "indent": 0, "parameters": [0, 53, 8, 17, 0, 0]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 31, "y": 30}, {"id": 23, "name": "EV023", "note": "<Hitbox Down: 9>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 57, 58, 16, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 11}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["Wave 8/VisuMZ_2_VisualBattleEnv", "BackEnvironmentRemove", "Back Environment: Remove", {"list:arraynum": "[\"1\"]"}]}, {"code": 657, "indent": 0, "parameters": ["ID(s) = [\"1\"]"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Water_Mist", "WATER: Mist", {"MainData": "", "powerTarget:eval": "3", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"1600\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"25\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"30\\\",\\\"scale:num\\\":\\\"0.75\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.2\\\",\\\"scaleRatioY:num\\\":\\\"0.3\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#888888\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 3"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"160…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "DARK: <PERSON>", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"180\\\",\\\"opacityVariance:num\\\":\\\"40\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.5\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"30\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"45\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"-3\\\",\\\"spinSpeedVariance:eval\\\":\\\"2\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeClouds", "DARK: Smoke Clouds", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"5\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"128\\\",\\\"opacityVariance:num\\\":\\\"24\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#00e1e1\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0.7\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"5\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 121, "indent": 0, "parameters": [65, 65, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 3}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 80%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 11}, null, null, null, null, null, null, null, null, null, null, null]}